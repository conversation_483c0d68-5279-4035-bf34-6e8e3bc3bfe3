#define onlySyncOnChange_BANDWIDTH_SAVING
using System.Collections.Generic;
using UnityEngine;
using Mirror;

/*
    Documentation: https://mirror-networking.gitbook.io/docs/components/network-transform
    API Reference: https://mirror-networking.com/docs/api/Mirror.NetworkTransformBase.html
*/

public class #SCRIPTNAME# : NetworkTransformBase
{
    #region Unity Callbacks

    protected override void Awake() { }

    protected override void OnValidate()
    {
        base.OnValidate();
    }

    /// <summary>
    /// This calls Reset()
    /// </summary>
    protected override void OnEnable()
    {
        base.OnEnable();
    }

    /// <summary>
    /// This calls Reset()
    /// </summary>
    protected override void OnDisable()
    {
        base.OnDisable();
    }

    #endregion

    #region NT Base Callbacks

    /// <summary>
    /// NTSnapshot struct is created here
    /// </summary>
    protected override TransformSnapshot Construct()
    {
        return base.Construct();
    }

    protected override Vector3 GetPosition()
    {
        return base.GetPosition();
    }

    protected override Quaternion GetRotation()
    {
        return base.GetRotation();
    }

    protected override Vector3 GetScale()
    {
        return base.GetScale();
    }

    protected override void SetPosition(Vector3 position)
    {
        base.SetPosition(position);
    }

    protected override void SetRotation(Quaternion rotation)
    {
        base.SetRotation(rotation);
    }

    protected override void SetScale(Vector3 scale)
    {
        base.SetScale(scale);
    }

    /// <summary>
    /// localPosition, localRotation, and localScale are set here:
    /// interpolated values are used if interpolation is enabled.
    /// goal values are used if interpolation is disabled.
    /// </summary>
    protected override void Apply(TransformSnapshot interpolated, TransformSnapshot endGoal)
    {
        base.Apply(interpolated, endGoal);
    }

    /// <summary>
    /// Called by both CmdTeleport and RpcTeleport on server and clients, respectively.
    /// Here you can disable a Character Controller before calling the base method,
    /// and re-enable it after the base method call to avoid conflicting with it.
    /// </summary>
    protected override void OnTeleport(Vector3 destination)
    {
        base.OnTeleport(destination);
    }

    /// <summary>
    /// Called by both CmdTeleport and RpcTeleport on server and clients, respectively.
    /// Here you can disable a Character Controller before calling the base method,
    /// and re-enable it after the base method call to avoid conflicting with it.
    /// </summary>
    protected override void OnTeleport(Vector3 destination, Quaternion rotation)
    {
        base.OnTeleport(destination, rotation);
    }

    /// <summary>
    /// Buffers are cleared and interpolation times are reset to zero here.
    /// This may be called when you are implementing some system of not sending
    /// if nothing changed, or just plain resetting if you have not received data
    /// for some time, as this will prevent a long interpolation period between old
    /// and just received data, as it will look like a lag. Reset() should also be
    /// called when authority is changed to another client or server, to prevent
    /// old buffers bugging out the interpolation if authority is changed back.
    /// </summary>
    public override void ResetState()
    {
        base.ResetState();
    }

    #endregion

    #region GUI

    // OnGUI allocates even if it does nothing. avoid in release.
#if UNITY_EDITOR || DEVELOPMENT_BUILD

    protected override void OnGUI()
    {
        base.OnGUI();
    }

    protected override void DrawGizmos(SortedList<double, TransformSnapshot> buffer)
    {
        base.DrawGizmos(buffer);
    }

    protected override void OnDrawGizmos()
    {
        base.OnDrawGizmos();
    }

#endif

    #endregion
}
