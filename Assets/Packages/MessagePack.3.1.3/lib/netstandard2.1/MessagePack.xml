<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MessagePack</name>
    </assembly>
    <members>
        <member name="T:System.Numerics.BitOperations">
            <summary>
            Utility methods for intrinsic bit-twiddling operations.
            The methods use hardware intrinsics when available on the underlying platform,
            otherwise they use optimized software fallbacks.
            </summary>
        </member>
        <member name="M:System.Numerics.BitOperations.RotateLeft(System.UInt32,System.Int32)">
            <summary>
            Rotates the specified value left by the specified number of bits.
            Similar in behavior to the x86 instruction ROL.
            </summary>
            <param name="value">The value to rotate.</param>
            <param name="offset">The number of bits to rotate by.
            Any value outside the range [0..31] is treated as congruent mod 32.</param>
            <returns>The rotated value.</returns>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute">
            <summary>
            Indicates that the specified method requires the ability to generate new code at runtime,
            for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when compiling ahead of time.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of dynamic code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of dynamic code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires dynamic code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute"/> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute"/>. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/>
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId"/>
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target"/> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId"/> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute">
            <summary>
            Indicates that the specified method requires dynamic access to code that is not referenced
            statically, for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when removing unreferenced
            code from an application.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of unreferenced code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of unreferenced code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires unreferenced code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute">
             <summary>
             Indicates that certain members on a specified <see cref="T:System.Type"/> are accessed dynamically,
             for example through <see cref="N:System.Reflection"/>.
             </summary>
             <remarks>
             This allows tools to understand which members are being accessed during the execution
             of a program.
            
             This attribute is valid on members whose type is <see cref="T:System.Type"/> or <see cref="T:System.String"/>.
            
             When this attribute is applied to a location of type <see cref="T:System.String"/>, the assumption is
             that the string represents a fully qualified type name.
            
             When this attribute is applied to a class, interface, or struct, the members specified
             can be accessed dynamically on <see cref="T:System.Type"/> instances returned from calling
             <see cref="M:System.Object.GetType"/> on instances of that class, interface, or struct.
            
             If the attribute is applied to a method it's treated as a special case and it implies
             the attribute should be applied to the "this" parameter of the method. As such the attribute
             should only be used on instance methods of types assignable to System.Type (or string, but no methods
             will use it there).
             </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute"/> class
            with the specified member types.
            </summary>
            <param name="memberTypes">The types of members dynamically accessed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members dynamically accessed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
            <summary>
            Specifies the types of members that are dynamically accessed. This enumeration
            has a System.FlagsAttribute attribute that allows a bitwise combination of its
            member values.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.Interfaces">
            <summary>
            Specifies all interfaces implemented by the type.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute">
            <summary>
            Indicates that the specified method parameter expects a constant.
            </summary>
            <remarks>
            This can be used to inform tooling that a constant should be used as an argument for the annotated parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute.Min">
            <summary>
            Indicates the minimum bound of the expected constant, inclusive.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute.Max">
            <summary>
            Indicates the maximum bound of the expected constant, inclusive.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute">
            <summary>
            Indicates that an API is experimental and it may change in the future.
            </summary>
            <remarks>
            This attribute allows call sites to be flagged with a diagnostic that indicates that an experimental
            feature is used. Authors can use this attribute to ship preview features in their assemblies.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute"/> class,
            specifying the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <param name="diagnosticId">The ID that the compiler will use when reporting a use of the API the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.DiagnosticId">
            <summary>
            Gets the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <value>The unique diagnostic ID.</value>
            <remarks>
            The diagnostic ID is shown in build output for warnings and errors.
            <para>This property represents the unique ID that can be used to suppress the warnings or errors, if needed.</para>
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.UrlFormat">
            <summary>
            Gets or sets the URL for corresponding documentation.
            The API accepts a format string instead of an actual URL, creating a generic URL that includes the diagnostic ID.
            </summary>
            <value>The format string that represents a URL to corresponding documentation.</value>
            <remarks>An example format string is <c>https://contoso.com/obsoletion-warnings/{0}</c>.</remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property members have not-null values.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>
            Initializes the attribute with a field or property member.
            </summary>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>
            Initializes the attribute with the list of field and property members.
            </summary>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property
            members have not-null values when returning with the specified return value condition.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>
            Initializes the attribute with the specified return value condition and a field or property member.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>
            Initializes the attribute with the specified return value condition and list of field and property members.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>
            Gets the return value condition.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.SetsRequiredMembersAttribute">
            <summary>
            Specifies that this constructor sets all required members for the current type,
            and callers do not need to set any required members themselves.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute">
            <summary>
            Specifies the syntax used in a string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String)">
            <summary>
            Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.
            </summary>
            <param name="syntax">The syntax identifier.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String,System.Object[])">
            <summary>Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.</summary>
            <param name="syntax">The syntax identifier.</param>
            <param name="arguments">Optional arguments associated with the specific syntax employed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Syntax">
            <summary>Gets the identifier of the syntax used.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Arguments">
            <summary>Optional arguments associated with the specific syntax employed.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.CompositeFormat">
            <summary>The syntax identifier for strings containing composite formats for string formatting.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateOnlyFormat">
            <summary>The syntax identifier for strings containing date format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateTimeFormat">
            <summary>The syntax identifier for strings containing date and time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.EnumFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Enum"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.GuidFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Guid"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Json">
            <summary>The syntax identifier for strings containing JavaScript Object Notation (JSON).</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.NumericFormat">
            <summary>The syntax identifier for strings containing numeric format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Regex">
            <summary>The syntax identifier for strings containing regular expressions.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeOnlyFormat">
            <summary>The syntax identifier for strings containing time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeSpanFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.TimeSpan"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Uri">
            <summary>The syntax identifier for strings containing URIs.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Xml">
            <summary>The syntax identifier for strings containing XML.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnscopedRefAttribute">
            <summary>
            Used to indicate a byref escapes and is not scoped.
            </summary>
            <remarks>
            <para>
            There are several cases where the C# compiler treats a <see langword="ref"/> as implicitly
            <see langword="scoped"/> - where the compiler does not allow the <see langword="ref"/> to escape the method.
            </para>
            <para>
            For example:
            <list type="number">
                <item><see langword="this"/> for <see langword="struct"/> instance methods.</item>
                <item><see langword="ref"/> parameters that refer to <see langword="ref"/> <see langword="struct"/> types.</item>
                <item><see langword="out"/> parameters.</item>
            </list>
            </para>
            <para>
            This attribute is used in those instances where the <see langword="ref"/> should be allowed to escape.
            </para>
            <para>
            Applying this attribute, in any form, has impact on consumers of the applicable API. It is necessary for
            API authors to understand the lifetime implications of applying this attribute and how it may impact their users.
            </para>
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute">
            <summary>
            An attribute that allows parameters to receive the expression of other parameters.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute"/> class.
            </summary>
            <param name="parameterName">The condition parameter value.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.ParameterName">
            <summary>
            Gets the parameter name the expression is retrieved from.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CollectionBuilderAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialize the attribute to refer to the <paramref name="methodName"/> method on the <paramref name="builderType"/> type.
            </summary>
            <param name="builderType">The type of the builder to use to construct the collection.</param>
            <param name="methodName">The name of the method on the builder to use to construct the collection.</param>
            <remarks>
            <paramref name="methodName"/> must refer to a static method that accepts a single parameter of
            type <see cref="T:System.ReadOnlySpan`1"/> and returns an instance of the collection being built containing
            a copy of the data from that span.  In future releases of .NET, additional patterns may be supported.
            </remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.BuilderType">
            <summary>
            Gets the type of the builder to use to construct the collection.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.MethodName">
            <summary>
            Gets the name of the method on the builder to use to construct the collection.
            </summary>
            <remarks>
            This should match the metadata name of the target method.
            For example, this might be ".ctor" if targeting the type's constructor.
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute">
            <summary>
            Indicates that compiler support for a particular feature is required for the location where this attribute is applied.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.#ctor(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute"/> type.
            </summary>
            <param name="featureName">The name of the feature to indicate.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName">
            <summary>
            The name of the compiler feature.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.IsOptional">
            <summary>
            If true, the compiler can choose to allow access to the location where this attribute is applied if it does not understand <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RefStructs">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the ref structs C# feature.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RequiredMembers">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the required members C# feature.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute">
            <summary>
            Indicates which arguments to a method involving an interpolated string handler should be passed to that handler.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="argument">The name of the argument that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="arguments">The names of the arguments that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.Arguments">
            <summary>
            Gets the names of the arguments that should be passed to the handler.
            </summary>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerAttribute">
            <summary>
            Indicates the attributed type is to be used as an interpolated string handler.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.IsExternalInit">
            <summary>
            Reserved to be used by the compiler for tracking metadata.
            This class should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ModuleInitializerAttribute">
             <summary>
             Used to indicate to the compiler that a method should be called
             in its containing module's initializer.
             </summary>
             <remarks>
             When one or more valid methods
             with this attribute are found in a compilation, the compiler will
             emit a module initializer which calls each of the attributed methods.
            
             Certain requirements are imposed on any method targeted with this attribute:
             - The method must be `static`.
             - The method must be an ordinary member method, as opposed to a property accessor, constructor, local function, etc.
             - The method must be parameterless.
             - The method must return `void`.
             - The method must not be generic or be contained in a generic type.
             - The method's effective accessibility must be `internal` or `public`.
            
             The specification for module initializers in the .NET runtime can be found here:
             https://github.com/dotnet/runtime/blob/main/docs/design/specs/Ecma-335-Augments.md#module-initializer
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute">
            <summary>
            Specifies the priority of a member in overload resolution. When unspecified, the default priority is 0.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute"/> class.
            </summary>
            <param name="priority">The priority of the attributed member. Higher numbers are prioritized, lower numbers are deprioritized. 0 is the default if no attribute is present.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute.Priority">
            <summary>
            The priority of the member.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ParamCollectionAttribute">
            <summary>
            Indicates that a method will allow a variable number of arguments in its invocation.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiredMemberAttribute">
            <summary>
            Specifies that a type has required members or that a member is required.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiresLocationAttribute">
            <summary>
            Reserved for use by a compiler for tracking metadata.
            This attribute should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.SkipLocalsInitAttribute">
            <summary>
            Used to indicate to the compiler that the <c>.locals init</c> flag should not be set in method headers.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class with the specified message.
            </summary>
            <param name="message">An optional message associated with this attribute instance.</param>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Message">
            <summary>
            Returns the optional message associated with this attribute instance.
            </summary>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Url">
            <summary>
            Returns the optional URL associated with this attribute instance.
            </summary>
        </member>
        <member name="T:MessagePack.BufferWriter">
            <summary>
            A fast access struct that wraps <see cref="T:System.Buffers.IBufferWriter`1"/>.
            </summary>
        </member>
        <member name="F:MessagePack.BufferWriter._output">
            <summary>
            The underlying <see cref="T:System.Buffers.IBufferWriter`1"/>.
            </summary>
        </member>
        <member name="F:MessagePack.BufferWriter._span">
            <summary>
            The result of the last call to <see cref="M:System.Buffers.IBufferWriter`1.GetSpan(System.Int32)"/>, less any bytes already "consumed" with <see cref="M:MessagePack.BufferWriter.Advance(System.Int32)"/>.
            Backing field for the <see cref="P:MessagePack.BufferWriter.Span"/> property.
            </summary>
        </member>
        <member name="F:MessagePack.BufferWriter._segment">
            <summary>
            The result of the last call to <see cref="M:System.Buffers.IBufferWriter`1.GetMemory(System.Int32)"/>, less any bytes already "consumed" with <see cref="M:MessagePack.BufferWriter.Advance(System.Int32)"/>.
            </summary>
        </member>
        <member name="F:MessagePack.BufferWriter._buffered">
            <summary>
            The number of uncommitted bytes (all the calls to <see cref="M:MessagePack.BufferWriter.Advance(System.Int32)"/> since the last call to <see cref="M:MessagePack.BufferWriter.Commit"/>).
            </summary>
        </member>
        <member name="F:MessagePack.BufferWriter._bytesCommitted">
            <summary>
            The total number of bytes written with this writer.
            Backing field for the <see cref="P:MessagePack.BufferWriter.BytesCommitted"/> property.
            </summary>
        </member>
        <member name="M:MessagePack.BufferWriter.#ctor(System.Buffers.IBufferWriter{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.BufferWriter"/> struct.
            </summary>
            <param name="output">The <see cref="T:System.Buffers.IBufferWriter`1"/> to be wrapped.</param>
        </member>
        <member name="M:MessagePack.BufferWriter.#ctor(MessagePack.SequencePool,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.BufferWriter"/> struct.
            </summary>
            <param name="sequencePool">The pool from which to draw an <see cref="T:System.Buffers.IBufferWriter`1"/> if required..</param>
            <param name="array">An array to start with so we can avoid accessing the <paramref name="sequencePool"/> if possible.</param>
        </member>
        <member name="P:MessagePack.BufferWriter.Span">
            <summary>
            Gets the result of the last call to <see cref="M:System.Buffers.IBufferWriter`1.GetSpan(System.Int32)"/>.
            </summary>
        </member>
        <member name="P:MessagePack.BufferWriter.BytesCommitted">
            <summary>
            Gets the total number of bytes written with this writer.
            </summary>
        </member>
        <member name="P:MessagePack.BufferWriter.UnderlyingWriter">
            <summary>
            Gets the <see cref="T:System.Buffers.IBufferWriter`1"/> underlying this instance.
            </summary>
        </member>
        <member name="M:MessagePack.BufferWriter.GetSpan(System.Int32)">
            <inheritdoc cref="M:System.Buffers.IBufferWriter`1.GetSpan(System.Int32)"/>
        </member>
        <member name="M:MessagePack.BufferWriter.Commit">
            <summary>
            Calls <see cref="M:System.Buffers.IBufferWriter`1.Advance(System.Int32)"/> on the underlying writer
            with the number of uncommitted bytes.
            </summary>
        </member>
        <member name="M:MessagePack.BufferWriter.Advance(System.Int32)">
            <summary>
            Used to indicate that part of the buffer has been written to.
            </summary>
            <param name="count">The number of bytes written to.</param>
        </member>
        <member name="M:MessagePack.BufferWriter.Write(System.ReadOnlySpan{System.Byte})">
            <summary>
            Copies the caller's buffer into this writer and calls <see cref="M:MessagePack.BufferWriter.Advance(System.Int32)"/> with the length of the source buffer.
            </summary>
            <param name="source">The buffer to copy in.</param>
        </member>
        <member name="M:MessagePack.BufferWriter.Ensure(System.Int32)">
            <summary>
            Acquires a new buffer if necessary to ensure that some given number of bytes can be written to a single buffer.
            </summary>
            <param name="count">The number of bytes that must be allocated in a single buffer.</param>
        </member>
        <member name="M:MessagePack.BufferWriter.TryGetUncommittedSpan(System.ReadOnlySpan{System.Byte}@)">
            <summary>
            Gets the span to the bytes written if they were never committed to the underlying buffer writer.
            </summary>
            <param name="span"></param>
            <returns></returns>
        </member>
        <member name="M:MessagePack.BufferWriter.EnsureMore(System.Int32)">
            <summary>
            Gets a fresh span to write to, with an optional minimum size.
            </summary>
            <param name="count">The minimum size for the next requested buffer.</param>
        </member>
        <member name="M:MessagePack.BufferWriter.WriteMultiBuffer(System.ReadOnlySpan{System.Byte})">
            <summary>
            Copies the caller's buffer into this writer, potentially across multiple buffers from the underlying writer.
            </summary>
            <param name="source">The buffer to copy into this writer.</param>
        </member>
        <member name="T:MessagePack.CompositeResolverAttribute">
            <summary>
            When applied to a <c>partial class</c> with the MessagePackAnalyzer package referenced,
            this attribute triggers a source generator that fills in the class with a perf-optimized
            implementation of an <see cref="T:MessagePack.IFormatterResolver"/>.
            </summary>
        </member>
        <member name="M:MessagePack.CompositeResolverAttribute.#ctor(System.Type[])">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.CompositeResolverAttribute"/> class
            that describes the composite resolver to generate.
            </summary>
            <param name="formattersAndResolvers">The list of formatters and resolvers that this resolver aggregates together.</param>
        </member>
        <member name="P:MessagePack.CompositeResolverAttribute.IncludeLocalFormatters">
            <summary>
            Gets or sets a value indicating whether to automatically include any formatters that are defined
            in the same assembly as the type to which this attribute is applied.
            </summary>
            <remarks>
            When <see langword="true"/>, the resolver will be a superset of the default source-generated resolver
            (which only includes formatters for data types defined in the same assembly as the type to which this attribute is applied)
            by adding any additional hand-written formatters declared in the same assembly for data types in other assemblies.
            </remarks>
        </member>
        <member name="T:MessagePack.Formatters.CollectionHelpers`2">
            <summary>
            Provides general helpers for creating collections (including dictionaries).
            </summary>
            <typeparam name="TCollection">The concrete type of collection to create.</typeparam>
            <typeparam name="TEqualityComparer">The type of equality comparer that we would hope to pass into the collection's constructor.</typeparam>
        </member>
        <member name="F:MessagePack.Formatters.CollectionHelpers`2.collectionCreator">
            <summary>
            The delegate that will create the collection, if the typical (int count, IEqualityComparer{T} equalityComparer) constructor was found.
            </summary>
        </member>
        <member name="M:MessagePack.Formatters.CollectionHelpers`2.#cctor">
            <summary>
            Initializes static members of the <see cref="T:MessagePack.Formatters.CollectionHelpers`2"/> class.
            </summary>
            <remarks>
            Initializes a delegate that is optimized to create a collection of a given size and using the given equality comparer, if possible.
            </remarks>
        </member>
        <member name="M:MessagePack.Formatters.CollectionHelpers`2.CreateHashCollection(System.Int32,`1)">
            <summary>
            Initializes a new instance of the <typeparamref name="TCollection"/> collection.
            </summary>
            <param name="count">The number of elements the collection should be prepared to receive.</param>
            <param name="equalityComparer">The equality comparer to initialize the collection with.</param>
            <returns>The newly initialized collection.</returns>
            <remarks>
            Use of the <paramref name="count"/> and <paramref name="equalityComparer"/> are a best effort.
            If we can't find a constructor on the collection in the expected shape, we'll just instantiate the collection with its default constructor.
            </remarks>
        </member>
        <member name="T:MessagePack.Formatters.NativeDateTimeFormatter">
            <summary>
            Serialize by .NET native DateTime binary format.
            </summary>
        </member>
        <member name="T:MessagePack.Formatters.DynamicObjectTypeFallbackFormatter">
            <summary>
            This formatter can serialize any value whose static type is <see cref="T:System.Object"/>
            for which another resolver can provide a formatter for the runtime type.
            Its deserialization is limited to forwarding all calls to the <see cref="T:MessagePack.Formatters.PrimitiveObjectFormatter"/>.
            </summary>
        </member>
        <member name="M:MessagePack.Formatters.EnumAsStringFormatter`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.Formatters.EnumAsStringFormatter`1"/> class
            that is case sensitive.
            </summary>
        </member>
        <member name="M:MessagePack.Formatters.EnumAsStringFormatter`1.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.Formatters.EnumAsStringFormatter`1"/> class.
            </summary>
            <param name="ignoreCase">A value indicating whether to allow enum value names to mismatch on deserialization.</param>
        </member>
        <member name="T:MessagePack.Formatters.IMessagePackFormatter">
            <summary>
            A base interface for <see cref="T:MessagePack.Formatters.IMessagePackFormatter`1"/> so that all generic implementations
            can be detected by a common base type.
            </summary>
        </member>
        <member name="T:MessagePack.Formatters.IMessagePackFormatter`1">
            <summary>
            The contract for serialization of some specific type.
            </summary>
            <typeparam name="T">The type to be serialized or deserialized.</typeparam>
        </member>
        <member name="M:MessagePack.Formatters.IMessagePackFormatter`1.Serialize(MessagePack.MessagePackWriter@,`0,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Serializes a value.
            </summary>
            <param name="writer">The writer to use when serializing the value.</param>
            <param name="value">The value to be serialized.</param>
            <param name="options">The serialization settings to use, including the resolver to use to obtain formatters for types that make up the composite type <typeparamref name="T"/>.</param>
        </member>
        <member name="M:MessagePack.Formatters.IMessagePackFormatter`1.Deserialize(MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Deserializes a value.
            </summary>
            <param name="reader">The reader to deserialize from.</param>
            <param name="options">The serialization settings to use, including the resolver to use to obtain formatters for types that make up the composite type <typeparamref name="T"/>.</param>
            <returns>The deserialized value.</returns>
        </member>
        <member name="T:MessagePack.Formatters.ByteArrayFormatter">
            <summary>
            Serializes a <see cref="T:System.Byte"/> array as a bin type.
            Deserializes a bin type or an array of byte-sized integers into a <see cref="T:System.Byte"/> array.
            </summary>
        </member>
        <member name="T:MessagePack.Formatters.TypeFormatter`1">
            <summary>
            Serializes any instance of <see cref="T:System.Type"/> by its <see cref="P:System.Type.AssemblyQualifiedName"/> value.
            </summary>
            <typeparam name="T">The <see cref="T:System.Type"/> class itself or a derived type.</typeparam>
        </member>
        <member name="T:MessagePack.Formatters.StringInterningFormatter">
            <summary>
            A <see cref="T:System.String" /> formatter that interns strings on deserialization.
            </summary>
        </member>
        <member name="M:MessagePack.Formatters.StringInterningFormatter.Deserialize(MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)">
            <inheritdoc/>
        </member>
        <member name="M:MessagePack.Formatters.StringInterningFormatter.Serialize(MessagePack.MessagePackWriter@,System.String,MessagePack.MessagePackSerializerOptions)">
            <inheritdoc/>
        </member>
        <member name="T:MessagePack.Formatters.ForceTypelessFormatter`1">
            <summary>
            Force serialize object as typeless.
            </summary>
        </member>
        <member name="T:MessagePack.Formatters.TypelessFormatter">
            <summary>
            For `object` field that holds derived from `object` value, ex: var arr = new object[] { 1, "a", new Model() };.
            </summary>
        </member>
        <member name="F:MessagePack.Formatters.TypelessFormatter.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="M:MessagePack.Formatters.TypelessFormatter.DeserializeByTypeName(System.ArraySegment{System.Byte},MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Does not support deserializing of anonymous types
            Type should be covered by preceeding resolvers in complex/standard resolver.
            </summary>
        </member>
        <member name="F:MessagePack.Formatters.NativeGuidFormatter.Instance">
            <summary>
            Unsafe binary Guid formatter. this is only allowed on LittleEndian environment.
            </summary>
        </member>
        <member name="F:MessagePack.Formatters.NativeDecimalFormatter.Instance">
            <summary>
            Unsafe binary Decimal formatter. this is only allows on LittleEndian environment.
            </summary>
        </member>
        <member name="T:MessagePack.GeneratedMessagePackResolverAttribute">
            <summary>
            An attribute to apply to a <see langword="partial" /> <see langword="class" /> that will serve as the
            source-generated resolver for MessagePack.
            </summary>
        </member>
        <member name="M:MessagePack.GeneratedMessagePackResolverAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.GeneratedMessagePackResolverAttribute"/> class.
            </summary>
        </member>
        <member name="P:MessagePack.GeneratedMessagePackResolverAttribute.UseMapMode">
            <summary>
            Gets or sets a value indicating whether types will be serialized with their property names as well as their
            values in a key=value dictionary, as opposed to an array of values.
            </summary>
        </member>
        <member name="T:MessagePack.IFormatterResolver">
            <summary>
            Allows querying for a formatter for serializing or deserializing a particular <see cref="T:System.Type" />.
            </summary>
        </member>
        <member name="M:MessagePack.IFormatterResolver.GetFormatter``1">
            <summary>
            Gets an <see cref="T:MessagePack.Formatters.IMessagePackFormatter`1"/> instance that can serialize or deserialize some type <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type of value to be serialized or deserialized.</typeparam>
            <returns>A formatter, if this resolver supplies one for type <typeparamref name="T"/>; otherwise <see langword="null"/>.</returns>
        </member>
        <member name="T:MessagePack.Internal.AutomataDictionary">
            <remarks>
            This code is used by dynamically generated code as well as AOT generated code,
            and thus must be public for the "C# generated and compiled into saved assembly" scenario.
            </remarks>
        </member>
        <member name="T:MessagePack.Internal.AutomataKeyGen">
            <remarks>
            This is used by dynamically generated code. It can be made internal after we enable our dynamic assemblies to access internals.
            But that trick may require net46, so maybe we should leave this as public.
            </remarks>
        </member>
        <member name="T:MessagePack.Internal.CodeGenHelpers">
            <summary>
            Helpers for generated code.
            </summary>
            <remarks>
            This code is used by dynamically generated code as well as AOT generated code,
            and thus must be public for the "C# generated and compiled into saved assembly" scenario.
            </remarks>
        </member>
        <member name="M:MessagePack.Internal.CodeGenHelpers.GetEncodedStringBytes(System.String)">
            <summary>
            Gets the messagepack encoding for a given string.
            </summary>
            <param name="value">The string to encode.</param>
            <returns>The messagepack encoding for <paramref name="value"/>, including messagepack header and UTF-8 bytes.</returns>
        </member>
        <member name="M:MessagePack.Internal.CodeGenHelpers.GetSpanFromSequence(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Gets a single <see cref="T:System.ReadOnlySpan`1"/> containing all bytes in a given <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            An array may be allocated if the bytes are not already contiguous in memory.
            </summary>
            <param name="sequence">The sequence to get a span for.</param>
            <returns>The span.</returns>
        </member>
        <member name="M:MessagePack.Internal.CodeGenHelpers.ReadStringSpan(MessagePack.MessagePackReader@)">
            <summary>
            Reads a string as a contiguous span of UTF-8 encoded characters.
            An array may be allocated if the string is not already contiguous in memory.
            </summary>
            <param name="reader">The reader to use.</param>
            <returns>The span of UTF-8 encoded characters.</returns>
        </member>
        <member name="M:MessagePack.Internal.CodeGenHelpers.GetArrayFromNullableSequence(System.Nullable{System.Buffers.ReadOnlySequence{System.Byte}}@)">
            <summary>
            Creates a <see cref="T:System.Byte"/> array for a given sequence, or <see langword="null" /> if the optional sequence is itself <see langword="null" />.
            </summary>
            <param name="sequence">The sequence.</param>
            <returns>The byte array or <see langword="null" /> .</returns>
        </member>
        <member name="M:MessagePack.Internal.DynamicAssembly.#ctor(System.String,System.Collections.Immutable.ImmutableHashSet{System.Reflection.AssemblyName})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.Internal.DynamicAssembly"/> class.
            Please use <see cref="T:MessagePack.Internal.DynamicAssemblyFactory"/> instead in order to work across different AssemblyLoadContext that may have duplicate modules.
            </summary>
            <param name="moduleName">Name of the module to be generated.</param>
            <param name="skipVisibilityChecksTo">The names of assemblies that should be fully accessible to this dynamic one, bypassing visibility checks.</param>
        </member>
        <member name="T:MessagePack.Internal.DynamicAssemblyFactory">
            <summary>
            This class is responsible for managing DynamicAssembly instance creation taking into account
            AssemblyLoadContext when running under .NET.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.DynamicAssemblyFactory.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.Internal.DynamicAssemblyFactory"/> class.
            </summary>
            <param name="moduleName">An arbitrary name that will be used for the module of the created dynamic assembly.</param>
        </member>
        <member name="M:MessagePack.Internal.ExpressionUtility.GetMethodInfo``1(System.Linq.Expressions.Expression{System.Func{``0}})">
            <summary>
            Get MethodInfo from Expression for Static(with result) method.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ExpressionUtility.GetMethodInfo(System.Linq.Expressions.Expression{System.Action})">
            <summary>
            Get MethodInfo from Expression for Static(void) method.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ExpressionUtility.GetMethodInfo``2(System.Linq.Expressions.Expression{System.Func{``0,``1}})">
            <summary>
            Get MethodInfo from Expression for Instance(with result) method.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ExpressionUtility.GetMethodInfo``1(System.Linq.Expressions.Expression{System.Action{``0}})">
            <summary>
            Get MethodInfo from Expression for Instance(void) method.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ExpressionUtility.GetMethodInfo``3(System.Linq.Expressions.Expression{System.Func{``0,``1,``2}})">
            <summary>
            Get MethodInfo from Expression for Instance(with result) method.
            </summary>
        </member>
        <member name="T:MessagePack.Internal.GeneratedAssemblyMessagePackResolverAttribute">
            <summary>
            An assembly-level attribute that identifies the source-generated resolver for MessagePack for all types in this assembly.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.GeneratedAssemblyMessagePackResolverAttribute.#ctor(System.Type,System.Int32,System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.Internal.GeneratedAssemblyMessagePackResolverAttribute"/> class.
            </summary>
            <param name="resolverType">The type that implements <see cref="T:MessagePack.IFormatterResolver"/>.</param>
            <param name="majorVersion">
            The <see cref="P:System.Version.Major"/> component of the version of the generator that produced the resolver and formatters.
            This may be used to determine whether the resolver and formatters are compatible with the current version of MessagePack.
            </param>
            <param name="minorVersion">
            The <see cref="P:System.Version.Minor"/> component of the version of the generator that produced the resolver and formatters.
            This may be used to determine whether the resolver and formatters are compatible with the current version of MessagePack.
            </param>
        </member>
        <member name="T:MessagePack.Internal.ILGeneratorExtensions">
            <summary>
            Provides optimized generation code and helpers.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitLdloc(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Loads the local variable at a specific index onto the evaluation stack.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitStloc(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Pops the current value from the top of the evaluation stack and stores it in a the local variable list at a specified index.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitLdloca(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Loads the address of the local variable at a specific index onto the evaluation statck.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitLdc_I4(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Pushes a supplied value of type int32 onto the evaluation stack as an int32.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitPop(System.Reflection.Emit.ILGenerator,System.Int32)">
            <summary>
            Helper for Pop op.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ILGeneratorExtensions.EmitIncrementFor(System.Reflection.Emit.ILGenerator,System.Reflection.Emit.LocalBuilder,System.Action{System.Reflection.Emit.LocalBuilder})">
            <summary>for  var i = 0, i ..., i++. </summary>
        </member>
        <member name="T:MessagePack.Internal.ThreadsafeTypeKeyHashTable`1">
            <summary>
            A dictionary where <see cref="T:System.Type"/> is the key, and a configurable <typeparamref name="TValue"/> type
            that is thread-safe to read and write, allowing concurrent reads and exclusive writes.
            </summary>
            <typeparam name="TValue">The type of value stored in the dictionary.</typeparam>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Boolean@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.SByte@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Int16@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Char@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.UInt16@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Int32@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.UInt32@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Int64@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.UInt64@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Single@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.UnsafeRefSerializeHelper.Serialize(MessagePack.MessagePackWriter@,System.Double@,System.Int32)">
            <summary>Unsafe serialize method without parameter checks nor cancellation.</summary>
            <param name="writer">MessagePackWriter.</param>
            <param name="input">Must not be null reference.</param>
            <param name="length">Must be greater than 0.</param>
        </member>
        <member name="M:MessagePack.Internal.DynamicObjectTypeBuilder.Matches(System.Reflection.MethodInfo,System.Int32,System.Type)">
            <summary>
            Helps match parameters when searching a method when the parameter is a generic type.
            </summary>
        </member>
        <member name="M:MessagePack.Internal.ObjectSerializationInfo.IsOptimizeTargetType(System.Type)">
            <devremarks>
            Keep this list in sync with ShouldUseFormatterResolverHelper.PrimitiveTypes.
            </devremarks>
        </member>
        <member name="P:MessagePack.Internal.ObjectSerializationInfo.EmittableMember.IsInitOnly">
            <summary>
            Gets a value indicating whether the property can only be set by an object initializer, a constructor, or another `init` member.
            </summary>
        </member>
        <member name="P:MessagePack.Internal.ObjectSerializationInfo.EmittableMember.IsExplicitContract">
            <summary>
            Gets or sets a value indicating whether this member is explicitly opted in with an attribute.
            </summary>
        </member>
        <member name="P:MessagePack.Internal.ObjectSerializationInfo.EmittableMember.IsProblematicInitProperty">
            <summary>
            Gets or sets a value indicating whether this member is a property with an <see langword="init" /> property setter
            that must be set via a <see cref="T:System.Reflection.Emit.DynamicMethod"/> rather than directly by a <see cref="T:System.Reflection.Emit.MethodBuilder"/>.
            </summary>
            <remarks>
            <see href="https://github.com/neuecc/MessagePack-CSharp/issues/1134">A bug</see> in <see cref="T:System.Reflection.Emit.MethodBuilder"/>
            blocks its ability to invoke property init accessors when in a generic class.
            </remarks>
        </member>
        <member name="T:MessagePack.LZ4.LZ4Codec">
            <summary>Unsafe LZ4 codec.</summary>
        </member>
        <member name="F:MessagePack.LZ4.LZ4Codec.MEMORY_USAGE">
            <summary>
            Memory usage formula : N->2^N Bytes (examples : 10 -> 1KB; 12 -> 4KB ; 16 -> 64KB; 20 -> 1MB; etc.)
            Increasing memory usage improves compression ratio
            Reduced memory usage can improve speed, due to cache effect
            Default value is 14, for 16KB, which nicely fits into Intel x86 L1 cache.
            </summary>
        </member>
        <member name="F:MessagePack.LZ4.LZ4Codec.NOTCOMPRESSIBLE_DETECTIONLEVEL">
            <summary>
            Decreasing this value will make the algorithm skip faster data segments considered "incompressible"
            This may decrease compression ratio dramatically, but will be faster on incompressible data
            Increasing this value will make the algorithm search more before declaring a segment "incompressible"
            This could improve compression a bit, but will be slower on incompressible data
            The default value (6) is recommended.
            </summary>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.MaximumOutputLength(System.Int32)">
            <summary>Gets maximum the length of the output.</summary>
            <param name="inputLength">Length of the input.</param>
            <returns>Maximum number of bytes needed for compressed buffer.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Encode(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
            <summary>Encodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="output">The output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.Decode(System.ReadOnlySpan{System.Byte},System.Span{System.Byte})">
            <summary>Decodes the specified input.</summary>
            <param name="input">The input.</param>
            <param name="output">The output.</param>
            <returns>Number of bytes written.</returns>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.BlockCopy32(System.Byte*,System.Byte*,System.Int32)">
            <summary>Copies block of memory.</summary>
            <param name="src">The source.</param>
            <param name="dst">The destination.</param>
            <param name="len">The length (in bytes).</param>
        </member>
        <member name="M:MessagePack.LZ4.LZ4Codec.BlockCopy64(System.Byte*,System.Byte*,System.Int32)">
            <summary>Copies block of memory.</summary>
            <param name="src">The source.</param>
            <param name="dst">The destination.</param>
            <param name="len">The length (in bytes).</param>
        </member>
        <member name="T:MessagePack.MessagePackType">
            <summary>
            https://github.com/msgpack/msgpack/blob/master/spec.md#serialization-type-to-format-conversion.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackCode">
            <summary>
            The core type codes as defined by msgpack.
            </summary>
            <seealso href="https://github.com/msgpack/msgpack/blob/master/spec.md#overview" />
        </member>
        <member name="M:MessagePack.MessagePackCode.IsSignedInteger(System.Byte)">
            <summary>
            Checks whether a given messagepack code represents an integer that might include a sign (i.e. might be a negative number).
            </summary>
            <param name="code">The messagepack code.</param>
            <returns>A boolean value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackCode.IsPositiveFixInt(System.Byte)">
            <summary>
            Checks whether a given messagepack code belong to positive fixint range.
            </summary>
            <param name="code">The messagepack code.</param>
            <returns>A boolean value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackCode.IsNegativeFixInt(System.Byte)">
            <summary>
            Checks whether a given messagepack code belong to negative fixint range.
            </summary>
            <param name="code">The messagepack code.</param>
            <returns>A boolean value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackCode.IsFixMap(System.Byte)">
            <summary>
            Checks whether a given messagepack code belong to fixmap range.
            </summary>
            <param name="code">The messagepack code.</param>
            <returns>A boolean value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackCode.IsFixArray(System.Byte)">
            <summary>
            Checks whether a given messagepack code belong to fixarray range.
            </summary>
            <param name="code">The messagepack code.</param>
            <returns>A boolean value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackCode.IsFixStr(System.Byte)">
            <summary>
            Checks whether a given messagepack code belong to fixstr range.
            </summary>
            <param name="code">The messagepack code.</param>
            <returns>A boolean value.</returns>
        </member>
        <member name="T:MessagePack.ReservedMessagePackExtensionTypeCode">
            <summary>
            The officially defined messagepack extension type codes.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackCompression">
            <summary>
            Identifies the various compression schemes which might be applied at the msgpack level.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackCompression.None">
            <summary>
            No compression is applied at the msgpack level.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackCompression.Lz4Block">
            <summary>
            Compresses an entire msgpack sequence as a single lz4 block format.
            This is the simple compression that achieves best compression ratio,
            at the cost of copying the entire sequence when necessary to get contiguous memory.
            </summary>
            <remarks>
            Uses msgpack type code ext99 and is compatible with v1 of this library.
            </remarks>
            <devremarks>
            See also ThisLibraryExtensionTypeCodes.Lz4Block.
            </devremarks>
        </member>
        <member name="F:MessagePack.MessagePackCompression.Lz4BlockArray">
            <summary>
            Compresses an entire msgpack sequence as a array of lz4 block format.
            This is compressed/decompressed in chunks that do not consume LOH,
            but the compression ratio is slightly sacrificed.
            </summary>
            <remarks>
            Uses msgpack type code ext98 in array.
            </remarks>
            <devremarks>
            See also ThisLibraryExtensionTypeCodes.Lz4BlockArray.
            </devremarks>
        </member>
        <member name="T:MessagePack.MessagePackCompressionExtensions">
            <summary>
            Extensions for <see cref="T:MessagePack.MessagePackCompression"/>.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackEventSource.Tasks">
            <summary>
            Names of constants in this class make up the middle term in the event name
            E.g.: MessagePack/InvokeMethod/Start.
            </summary>
            <remarks>Name of this class is important for EventSource.</remarks>
        </member>
        <member name="T:MessagePack.MessagePackEventSourceExtensions">
            <summary>
            Helper methods for <see cref="T:MessagePack.MessagePackEventSource"/>.
            </summary>
            <remarks>
            This methods may contain parameter types that are not allowed on the
            <see cref="T:MessagePack.MessagePackEventSource"/> class itself.
            If these methods were to be moved to the class itself,
            eventing would silently fail at runtime, observable only by watching the events (e.g. with PerfView).
            </remarks>
        </member>
        <member name="T:MessagePack.MessagePackPrimitives">
            <summary>
            Primitive msgpack encoding/decoding methods.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackPrimitives.DecodeResult">
            <summary>
            Enumerates the possible outcomes of a read operation.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackPrimitives.DecodeResult.Success">
            <summary>
            The token was successfully read from the buffer.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackPrimitives.DecodeResult.TokenMismatch">
            <summary>
            The token read from the buffer did not match the expected token.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackPrimitives.DecodeResult.EmptyBuffer">
            <summary>
            The buffer is empty and no token could be read.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackPrimitives.DecodeResult.InsufficientBuffer">
            <summary>
            The token is of the expected type, but the buffer does not include all the bytes needed to read the value.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadNil(System.ReadOnlySpan{System.Byte},System.Int32@)">
            <summary>
            Tries to read a nil value from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads a <see cref="F:MessagePack.MessagePackCode.Nil"/> value from the buffer.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadArrayHeader(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)">
            <summary>
            Tries to read the header of an array from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="count">Receives the number of elements in the array, if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads an array header from
            <see cref="F:MessagePack.MessagePackCode.Array16"/>,
            <see cref="F:MessagePack.MessagePackCode.Array32"/>, or
            some built-in code between <see cref="F:MessagePack.MessagePackCode.MinFixArray"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixArray"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadMapHeader(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)">
            <summary>
            Tries to read the header of a map from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="count">Receives the number of key-value pairs in the map, if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads a map header from
            <see cref="F:MessagePack.MessagePackCode.Map16"/>,
            <see cref="F:MessagePack.MessagePackCode.Map32"/>, or
            some built-in code between <see cref="F:MessagePack.MessagePackCode.MinFixMap"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixMap"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBool(System.ReadOnlySpan{System.Byte},System.Boolean@,System.Int32@)">
            <summary>
            Tries to read a boolean value from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="value">Receives the boolean value if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads a <see cref="F:MessagePack.MessagePackCode.True"/> or <see cref="F:MessagePack.MessagePackCode.False"/> value from the buffer.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadChar(System.ReadOnlySpan{System.Byte},System.Char@,System.Int32@)">
            <summary>
            Tries to read a singly encoded character from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="value">Receives the character if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads a ushort value using <see cref="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.UInt16@)"/> from the buffer and interprets it as a <see langword="char" />.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadDateTime(System.ReadOnlySpan{System.Byte},System.DateTime@,System.Int32@)">
            <summary>
            Tries to read a <see cref="T:System.DateTime"/> from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="value">Receives the timestamp if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads the extension header using <see cref="M:MessagePack.MessagePackPrimitives.TryReadExtensionHeader(System.ReadOnlySpan{System.Byte},MessagePack.ExtensionHeader@,System.Int32@)"/>
            then the extension itself using <see cref="M:MessagePack.MessagePackPrimitives.TryReadDateTime(System.ReadOnlySpan{System.Byte},MessagePack.ExtensionHeader,System.DateTime@,System.Int32@)"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadDateTime(System.ReadOnlySpan{System.Byte},MessagePack.ExtensionHeader,System.DateTime@,System.Int32@)">
            <summary>
            Tries to read a <see cref="T:System.DateTime"/> from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="header">The extension header that introduces the timestamp. This extension is expected to carry a <see cref="P:MessagePack.ExtensionHeader.TypeCode"/> value of <see cref="F:MessagePack.ReservedMessagePackExtensionTypeCode.DateTime"/>.</param>
            <param name="value">Receives the timestamp if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadExtensionHeader(System.ReadOnlySpan{System.Byte},MessagePack.ExtensionHeader@,System.Int32@)">
            <summary>
            Tries to read the header of an extension from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="extensionHeader">Receives the extension header, if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            Reads an extension header from
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBinHeader(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)">
            <summary>
            Tries to read the header of a binary data segment from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="length">Receives the length of the binary data, if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            <para>
            Reads a binary data header from
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>.
            </para>
            <para>
            Note that in the original msgpack spec, there were no binary data headers, so binary data was
            introduced using string headers.
            This should be read using <see cref="M:MessagePack.MessagePackPrimitives.TryReadStringHeader(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)"/>.
            </para>
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadStringHeader(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)">
            <summary>
            Tries to read the header of a string from the specified buffer.
            </summary>
            <param name="source">The buffer to read from.</param>
            <param name="length">Receives the length of the string, if successful.</param>
            <param name="tokenSize">Receives the number of bytes read from the source, or the minimum length of <paramref name="source"/> required to read the data.</param>
            <returns>The result classification of the read operation.</returns>
            <remarks>
            <para>
            Reads a string header from
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>,
            or any value between <see cref="F:MessagePack.MessagePackCode.MinFixStr"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixStr"/>.
            </para>
            <para>
            Note that in the original msgpack spec, there were no binary data headers, so binary data was
            introduced using string headers.
            </para>
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.UInt16@)">
            <summary>
            Reads an <see cref="T:System.UInt16"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.UInt16"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.Int16@)">
            <summary>
            Reads a <see cref="T:System.Int16"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for a <see cref="T:System.Int16"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.UInt32@)">
            <summary>
            Reads a <see cref="T:System.UInt32"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.UInt32"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.Int32@)">
            <summary>
            Reads an <see cref="T:System.Int32"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.Int32"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.UInt64@)">
            <summary>
            Reads a <see cref="T:System.UInt32"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.UInt32"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadBigEndian(System.ReadOnlySpan{System.Byte},System.Int64@)">
            <summary>
            Reads a <see cref="T:System.Int64"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.Int64"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadByte(System.ReadOnlySpan{System.Byte},System.Byte@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.Byte"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadUInt16(System.ReadOnlySpan{System.Byte},System.UInt16@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.UInt16"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadUInt32(System.ReadOnlySpan{System.Byte},System.UInt32@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.UInt32"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadUInt64(System.ReadOnlySpan{System.Byte},System.UInt64@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.UInt64"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadSByte(System.ReadOnlySpan{System.Byte},System.SByte@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.SByte"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadInt16(System.ReadOnlySpan{System.Byte},System.Int16@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.Int16"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadInt32(System.ReadOnlySpan{System.Byte},System.Int32@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.Int32"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadInt64(System.ReadOnlySpan{System.Byte},System.Int64@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.Int64"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadSingle(System.ReadOnlySpan{System.Byte},System.Single@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.Single"/> value from a <see cref="F:MessagePack.MessagePackCode.Float64"/> or <see cref="F:MessagePack.MessagePackCode.Float32"/>
            or any of the integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryReadDouble(System.ReadOnlySpan{System.Byte},System.Double@,System.Int32@)">
            <summary>
            Tries to read an <see cref="T:System.Double"/> value from a <see cref="F:MessagePack.MessagePackCode.Float64"/> or <see cref="F:MessagePack.MessagePackCode.Float32"/>
            or any of the integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteNil(System.Span{System.Byte},System.Int32@)">
            <summary>
            Write a nil value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 1 byte in length to ensure success.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> is indicates how many bytes are required to write the value successfully.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteArrayHeader(System.Span{System.Byte},System.UInt32,System.Int32@)">
            <summary>
            Writes the array header to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success with any value for <paramref name="count"/>.</param>
            <param name="count">The number of elements in the array.</param>
            <param name="bytesWritten">The number of bytes required to write the array header, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the array header written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the array header successfully.
            </returns>
            <remarks>
            Writes the length of the next array to be written in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixArray"/>,
            <see cref="F:MessagePack.MessagePackCode.Array16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Array32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteMapHeader(System.Span{System.Byte},System.UInt32,System.Int32@)">
            <summary>
            Writes the map header to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success with any value for <paramref name="count"/>.</param>
            <param name="count">The number of key-value pairs in the map.</param>
            <param name="bytesWritten">The number of bytes required to write the map header, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the map header written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the map header successfully.
            </returns>
            <remarks>
            Writes the length of the next map to be written in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixMap"/>,
            <see cref="F:MessagePack.MessagePackCode.Map16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Map32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.SByte,System.Int32@)">
            <summary>
            Writes a signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 2 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Int16,System.Int32@)">
            <summary>
            Writes a signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 3 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Int32,System.Int32@)">
            <summary>
            Writes a signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Int64,System.Int32@)">
            <summary>
            Writes a signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 9 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt64"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int64"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteInt8(System.Span{System.Byte},System.SByte,System.Int32@)">
            <summary>
            Writes an 8-bit signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 2 bytes in length.</param>
            <param name="value">The 8-bit signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.Int8"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteInt16(System.Span{System.Byte},System.Int16,System.Int32@)">
            <summary>
            Writes a 16-bit signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 3 bytes in length to ensure success.</param>
            <param name="value">The 16-bit signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.Int16"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteInt32(System.Span{System.Byte},System.Int32,System.Int32@)">
            <summary>
            Writes a 32-bit signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success.</param>
            <param name="value">The 32-bit signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.Int32"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteInt64(System.Span{System.Byte},System.Int64,System.Int32@)">
            <summary>
            Writes a 64-bit signed integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 9 bytes in length to ensure success.</param>
            <param name="value">The 64-bit signed integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.Int64"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Byte,System.Int32@)">
            <summary>
            Writes an unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 2 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.UInt16,System.Int32@)">
            <summary>
            Writes an unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 3 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.UInt32,System.Int32@)">
            <summary>
            Writes an unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.UInt64,System.Int32@)">
            <summary>
            Writes an unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 9 bytes in length to ensure success with any value for <paramref name="value"/>.</param>
            <param name="value">The unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The smallest possible representation is used for the value, which may be as few as 1 byte.
            In addition to the built-in 1-byte code when within specific MessagePack-supported ranges,
            any of the following encodings may also be used:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt64"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteUInt8(System.Span{System.Byte},System.Byte,System.Int32@)">
            <summary>
            Writes an 8-bit unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 2 bytes in length.</param>
            <param name="value">The 8-bit unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.UInt8"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteUInt16(System.Span{System.Byte},System.UInt16,System.Int32@)">
            <summary>
            Writes an 16-bit unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 3 bytes in length.</param>
            <param name="value">The 16-bit unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.UInt16"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteUInt32(System.Span{System.Byte},System.UInt32,System.Int32@)">
            <summary>
            Writes a 32-bit unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length.</param>
            <param name="value">The 32-bit unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.UInt32"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteUInt64(System.Span{System.Byte},System.UInt64,System.Int32@)">
            <summary>
            Writes a 64-bit unsigned integer value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 9 bytes in length.</param>
            <param name="value">The 64-bit unsigned integer value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.UInt64"/> encoding is always used, even if the value could be encoded in fewer bytes.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Single,System.Int32@)">
            <summary>
            Writes a single-precision floating-point value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success.</param>
            <param name="value">The single-precision floating-point value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.Float32"/> encoding is always used.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Double,System.Int32@)">
            <summary>
            Writes a double-precision floating-point value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 9 bytes in length to ensure success.</param>
            <param name="value">The double-precision floating-point value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The <see cref="F:MessagePack.MessagePackCode.Float64"/> encoding is always used.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Boolean,System.Int32@)">
            <summary>
            Writes a boolean value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 1 byte in length to ensure success.</param>
            <param name="value">The boolean value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.Char,System.Int32@)">
            <summary>
            Writes a character value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 3 bytes in length to ensure success.</param>
            <param name="value">The character value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            A character is encoded as a 16-bit unsigned integer, in its most compact form.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWrite(System.Span{System.Byte},System.DateTime,System.Int32@)">
            <summary>
            Writes a <see cref="T:System.DateTime"/> value to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 315 bytes in length to ensure success.</param>
            <param name="value">The <see cref="T:System.DateTime"/> value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
            <remarks>
            The value is encoded as an extension type with a type code of <see cref="F:MessagePack.ReservedMessagePackExtensionTypeCode.DateTime"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteBinHeader(System.Span{System.Byte},System.UInt32,System.Int32@)">
            <summary>
            Writes the binary header to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success.</param>
            <param name="length">The length of the binary data.</param>
            <param name="bytesWritten">The number of bytes required to write the binary header, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the binary header written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the binary header successfully.
            </returns>
            <remarks>
            Writes the length of the binary data in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteStringHeader(System.Span{System.Byte},System.UInt32,System.Int32@)">
            <summary>
            Writes the header for a UTF-8 encoded string to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success.</param>
            <param name="byteCount">The number of UTF-8 encoded bytes in the string.</param>
            <param name="bytesWritten">The number of bytes required to write the string header, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the string header written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the string header successfully.
            </returns>
            <remarks>
            Writes the length of the string in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixStr"/>,
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Str32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteExtensionFormatHeader(System.Span{System.Byte},MessagePack.ExtensionHeader,System.Int32@)">
            <summary>
            Writes an extension header to the specified buffer, if the buffer is large enough.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 6 bytes in length to ensure success.</param>
            <param name="extensionHeader">The extension header to write.</param>
            <param name="bytesWritten">The number of bytes required to write the binary header, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the binary header written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the binary header successfully.
            </returns>
            <remarks>
            Writes the header of the extension data using the most compact form of
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteFixIntUnsafe(System.Span{System.Byte},System.Byte,System.Int32@)">
            <summary>
            Writes a very small integer into just one byte of msgpack data.
            This method does *not* ensure that the value is within the range of a fixint.
            The caller must ensure that the value is less than or equal to <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success.</param>
            <param name="value">The single-precision floating-point value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.TryWriteNegativeFixIntUnsafe(System.Span{System.Byte},System.SByte,System.Int32@)">
            <summary>
            Writes a very small magnitude negative integer into just one byte of msgpack data.
            This method does *not* ensure that the value is within the range of a fixint.
            The caller must ensure that the value is between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/>
            and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>, inclusive.
            </summary>
            <param name="destination">The buffer to write to. This should be at least 5 bytes in length to ensure success.</param>
            <param name="value">The single-precision floating-point value to write.</param>
            <param name="bytesWritten">The number of bytes required to write the value, whether successful or not.</param>
            <returns>
            <see langword="true" /> if <paramref name="destination"/> was large enough and the value written; otherwise, <see langword="false" />.
            When <see langword="false"/>, the value of <paramref name="bytesWritten"/> indicates how many bytes are required to write the value successfully.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.WriteBigEndian(System.Span{System.Byte},System.UInt16)">
            <summary>
            Writes an integer value in big-endian order to the specified buffer.
            </summary>
            <param name="destination">The buffer to write to.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.WriteBigEndian(System.Span{System.Byte},System.UInt32)">
            <summary>
            Writes an integer value in big-endian order to the specified buffer.
            </summary>
            <param name="destination">The buffer to write to.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.WriteBigEndian(System.Span{System.Byte},System.UInt64)">
            <summary>
            Writes an integer value in big-endian order to the specified buffer.
            </summary>
            <param name="destination">The buffer to write to.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.WriteBigEndian(System.Span{System.Byte},System.Int16)">
            <summary>
            Writes an integer value in big-endian order to the specified buffer.
            </summary>
            <param name="destination">The buffer to write to.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.WriteBigEndian(System.Span{System.Byte},System.Int32)">
            <summary>
            Writes an integer value in big-endian order to the specified buffer.
            </summary>
            <param name="destination">The buffer to write to.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackPrimitives.WriteBigEndian(System.Span{System.Byte},System.Int64)">
            <summary>
            Writes an integer value in big-endian order to the specified buffer.
            </summary>
            <param name="destination">The buffer to write to.</param>
            <param name="value">The value to write.</param>
        </member>
        <member name="T:MessagePack.MessagePackReader">
            <summary>
            A primitive types reader for the MessagePack format.
            </summary>
            <remarks>
            <see href="https://github.com/msgpack/msgpack/blob/master/spec.md">The MessagePack spec.</see>.
            </remarks>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when reading methods fail due to invalid data.</exception>
            <exception cref="T:System.IO.EndOfStreamException">Thrown by reading methods when there are not enough bytes to read the required value.</exception>
        </member>
        <member name="F:MessagePack.MessagePackReader.reader">
            <summary>
            The reader over the sequence.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackReader.#ctor(System.ReadOnlyMemory{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackReader"/> struct.
            </summary>
            <param name="memory">The buffer to read from.</param>
        </member>
        <member name="M:MessagePack.MessagePackReader.#ctor(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackReader"/> struct.
            </summary>
            <param name="readOnlySequence">The sequence to read from.</param>
        </member>
        <member name="P:MessagePack.MessagePackReader.CancellationToken">
            <summary>
            Gets or sets the cancellation token for this deserialization operation.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.Depth">
            <summary>
            Gets or sets the present depth of the object graph being deserialized.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.Sequence">
            <summary>
            Gets the <see cref="T:System.Buffers.ReadOnlySequence`1"/> originally supplied to the constructor.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.Position">
            <summary>
            Gets the current position of the reader within <see cref="P:MessagePack.MessagePackReader.Sequence"/>.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.Consumed">
            <summary>
            Gets the number of bytes consumed by the reader.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.End">
            <summary>
            Gets a value indicating whether the reader is at the end of the sequence.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.IsNil">
            <summary>
            Gets a value indicating whether the reader position is pointing at a nil value.
            </summary>
            <exception cref="T:System.IO.EndOfStreamException">Thrown if the end of the sequence provided to the constructor is reached before the expected end of the data.</exception>
        </member>
        <member name="P:MessagePack.MessagePackReader.NextMessagePackType">
            <summary>
            Gets the next message pack type to be read.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackReader.NextCode">
            <summary>
            Gets the type of the next MessagePack block.
            </summary>
            <exception cref="T:System.IO.EndOfStreamException">Thrown if the end of the sequence provided to the constructor is reached before the expected end of the data.</exception>
            <remarks>
            See <see cref="T:MessagePack.MessagePackCode"/> for valid message pack codes and ranges.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.Clone(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackReader"/> struct,
            with the same settings as this one, but with its own buffer to read from.
            </summary>
            <param name="readOnlySequence">The sequence to read from.</param>
            <returns>The new reader.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.CreatePeekReader">
            <summary>
            Creates a new <see cref="T:MessagePack.MessagePackReader"/> at this reader's current position.
            The two readers may then be used independently without impacting each other.
            </summary>
            <returns>A new <see cref="T:MessagePack.MessagePackReader"/>.</returns>
            <devremarks>
            Since this is a struct, copying it completely is as simple as returning itself
            from a property that isn't a "ref return" property.
            </devremarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.Skip">
            <summary>
            Advances the reader to the next MessagePack structure to be read.
            </summary>
            <remarks>
            The entire structure is skipped, including content of maps or arrays, or any other type with payloads.
            To get the raw MessagePack sequence that was skipped, use <see cref="M:MessagePack.MessagePackReader.ReadRaw"/> instead.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.TrySkip">
            <summary>
            Advances the reader to the next MessagePack structure to be read.
            </summary>
            <returns><see langword="true"/> if the entire structure beginning at the current <see cref="P:MessagePack.MessagePackReader.Position"/> is found in the <see cref="P:MessagePack.MessagePackReader.Sequence"/>; <see langword="false"/> otherwise.</returns>
            <remarks>
            The entire structure is skipped, including content of maps or arrays, or any other type with payloads.
            To get the raw MessagePack sequence that was skipped, use <see cref="M:MessagePack.MessagePackReader.ReadRaw"/> instead.
            WARNING: when false is returned, the position of the reader is undefined.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadNil">
            <summary>
            Reads a <see cref="F:MessagePack.MessagePackCode.Nil"/> value.
            </summary>
            <returns>A nil value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.TryReadNil">
            <summary>
            Reads nil if it is the next token.
            </summary>
            <returns><see langword="true"/> if the next token was nil; <see langword="false"/> otherwise.</returns>
            <exception cref="T:System.IO.EndOfStreamException">Thrown if the end of the sequence provided to the constructor is reached before the expected end of the data.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadRaw(System.Int64)">
            <summary>
            Reads a sequence of bytes without any decoding.
            </summary>
            <param name="length">The number of bytes to read.</param>
            <returns>The sequence of bytes read.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadRaw">
            <summary>
            Reads the next MessagePack primitive.
            </summary>
            <returns>The raw MessagePack sequence.</returns>
            <remarks>
            The entire primitive is read, including content of maps or arrays, or any other type with payloads.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadArrayHeader">
            <summary>
            Read an array header from
            <see cref="F:MessagePack.MessagePackCode.Array16"/>,
            <see cref="F:MessagePack.MessagePackCode.Array32"/>, or
            some built-in code between <see cref="F:MessagePack.MessagePackCode.MinFixArray"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixArray"/>.
            </summary>
            <exception cref="T:System.IO.EndOfStreamException">
            Thrown if the header cannot be read in the bytes left in the <see cref="P:MessagePack.MessagePackReader.Sequence"/>
            or if it is clear that there are insufficient bytes remaining after the header to include all the elements the header claims to be there.
            </exception>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if a code other than an array header is encountered.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.TryReadArrayHeader(System.Int32@)">
            <summary>
            Reads an array header from
            <see cref="F:MessagePack.MessagePackCode.Array16"/>,
            <see cref="F:MessagePack.MessagePackCode.Array32"/>, or
            some built-in code between <see cref="F:MessagePack.MessagePackCode.MinFixArray"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixArray"/>
            if there is sufficient buffer to read it.
            </summary>
            <param name="count">Receives the number of elements in the array if the entire array header could be read.</param>
            <returns><see langword="true"/> if there was sufficient buffer and an array header was found; <see langword="false"/> if the buffer incompletely describes an array header.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if a code other than an array header is encountered.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadMapHeader">
            <summary>
            Read a map header from
            <see cref="F:MessagePack.MessagePackCode.Map16"/>,
            <see cref="F:MessagePack.MessagePackCode.Map32"/>, or
            some built-in code between <see cref="F:MessagePack.MessagePackCode.MinFixMap"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixMap"/>.
            </summary>
            <returns>The number of key=value pairs in the map.</returns>
            <exception cref="T:System.IO.EndOfStreamException">
            Thrown if the header cannot be read in the bytes left in the <see cref="P:MessagePack.MessagePackReader.Sequence"/>
            or if it is clear that there are insufficient bytes remaining after the header to include all the elements the header claims to be there.
            </exception>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if a code other than an map header is encountered.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.TryReadMapHeader(System.Int32@)">
            <summary>
            Reads a map header from
            <see cref="F:MessagePack.MessagePackCode.Map16"/>,
            <see cref="F:MessagePack.MessagePackCode.Map32"/>, or
            some built-in code between <see cref="F:MessagePack.MessagePackCode.MinFixMap"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixMap"/>
            if there is sufficient buffer to read it.
            </summary>
            <param name="count">Receives the number of key=value pairs in the map if the entire map header can be read.</param>
            <returns><see langword="true"/> if there was sufficient buffer and a map header was found; <see langword="false"/> if the buffer incompletely describes an map header.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if a code other than an map header is encountered.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadBoolean">
            <summary>
            Reads a boolean value from either a <see cref="F:MessagePack.MessagePackCode.False"/> or <see cref="F:MessagePack.MessagePackCode.True"/>.
            </summary>
            <returns>The value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadChar">
            <summary>
            Reads a <see cref="T:System.Char"/> from any of:
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            or anything between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>.
            </summary>
            <returns>A character.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadSingle">
            <summary>
            Reads an <see cref="T:System.Single"/> value from any value encoded with:
            <see cref="F:MessagePack.MessagePackCode.Float32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int64"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt64"/>,
            or some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            or some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>.
            </summary>
            <returns>The value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadDouble">
            <summary>
            Reads an <see cref="T:System.Double"/> value from any value encoded with:
            <see cref="F:MessagePack.MessagePackCode.Float64"/>,
            <see cref="F:MessagePack.MessagePackCode.Float32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int64"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt64"/>,
            or some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            or some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>.
            </summary>
            <returns>The value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadDateTime">
            <summary>
            Reads a <see cref="T:System.DateTime"/> from a value encoded with
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>.
            Expects extension type code <see cref="F:MessagePack.ReservedMessagePackExtensionTypeCode.DateTime"/>.
            </summary>
            <returns>The value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadDateTime(MessagePack.ExtensionHeader)">
            <summary>
            Reads a <see cref="T:System.DateTime"/> from a value encoded with
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>.
            Expects extension type code <see cref="F:MessagePack.ReservedMessagePackExtensionTypeCode.DateTime"/>.
            </summary>
            <param name="header">The extension header that was already read.</param>
            <returns>The value.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadBytes">
            <summary>
            Reads a span of bytes, whose length is determined by a header of one of these types:
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>,
            or to support OldSpec compatibility:
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>,
            or something between <see cref="F:MessagePack.MessagePackCode.MinFixStr"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixStr"/>.
            </summary>
            <returns>
            A sequence of bytes, or <see langword="null"/> if the read token is <see cref="F:MessagePack.MessagePackCode.Nil"/>.
            The data is a slice from the original sequence passed to this reader's constructor.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadStringSequence">
            <summary>
            Reads a string of bytes, whose length is determined by a header of one of these types:
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>,
            or a code between <see cref="F:MessagePack.MessagePackCode.MinFixStr"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixStr"/>.
            </summary>
            <returns>
            The sequence of bytes, or <see langword="null"/> if the read token is <see cref="F:MessagePack.MessagePackCode.Nil"/>.
            The data is a slice from the original sequence passed to this reader's constructor.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.TryReadStringSpan(System.ReadOnlySpan{System.Byte}@)">
            <summary>
            Reads a string of bytes, whose length is determined by a header of one of these types:
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>,
            or a code between <see cref="F:MessagePack.MessagePackCode.MinFixStr"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixStr"/>.
            </summary>
            <param name="span">Receives the span to the string.</param>
            <returns>
            <see langword="true"/> if the string is contiguous in memory such that it could be set as a single span.
            <see langword="false"/> if the read token is <see cref="F:MessagePack.MessagePackCode.Nil"/> or the string is not in a contiguous span.
            </returns>
            <remarks>
            Callers should generally be prepared for a <see langword="false"/> result and failover to calling <see cref="M:MessagePack.MessagePackReader.ReadStringSequence"/>
            which can represent a <see langword="null"/> result and handle strings that are not contiguous in memory.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadString">
            <summary>
            Reads a string, whose length is determined by a header of one of these types:
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>,
            or a code between <see cref="F:MessagePack.MessagePackCode.MinFixStr"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixStr"/>.
            </summary>
            <returns>A string, or <see langword="null"/> if the current msgpack token is <see cref="F:MessagePack.MessagePackCode.Nil"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadExtensionFormatHeader">
            <summary>
            Reads an extension format header, based on one of these codes:
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>.
            </summary>
            <returns>The extension header.</returns>
            <exception cref="T:System.IO.EndOfStreamException">
            Thrown if the header cannot be read in the bytes left in the <see cref="P:MessagePack.MessagePackReader.Sequence"/>
            or if it is clear that there are insufficient bytes remaining after the header to include all the bytes the header claims to be there.
            </exception>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if a code other than an extension format header is encountered.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.TryReadExtensionFormatHeader(MessagePack.ExtensionHeader@)">
            <summary>
            Reads an extension format header, based on one of these codes:
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>
            if there is sufficient buffer to read it.
            </summary>
            <param name="extensionHeader">Receives the extension header if the remaining bytes in the <see cref="P:MessagePack.MessagePackReader.Sequence"/> fully describe the header.</param>
            <returns>The number of key=value pairs in the map.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if a code other than an extension format header is encountered.</exception>
            <remarks>
            When this method returns <see langword="false"/> the position of the reader is left in an undefined position.
            The caller is expected to recreate the reader (presumably with a longer sequence to read from) before continuing.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadExtensionFormat">
            <summary>
            Reads an extension format header and data, based on one of these codes:
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>.
            </summary>
            <returns>
            The extension format.
            The data is a slice from the original sequence passed to this reader's constructor.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ThrowNotEnoughBytesException">
            <summary>
            Throws an exception indicating that there aren't enough bytes remaining in the buffer to store
            the promised data.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackReader.ThrowNotEnoughBytesException(System.Exception)">
            <summary>
            Throws an exception indicating that there aren't enough bytes remaining in the buffer to store
            the promised data.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackReader.ThrowInvalidCode(System.Byte)">
            <summary>
            Throws an <see cref="T:MessagePack.MessagePackSerializationException"/> explaining an unexpected code was encountered.
            </summary>
            <param name="code">The code that was encountered.</param>
            <returns>Nothing. This method always throws.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ThrowInsufficientBufferUnless(System.Boolean)">
            <summary>
            Throws <see cref="T:System.IO.EndOfStreamException"/> if a condition is false.
            </summary>
            <param name="condition">A boolean value.</param>
            <exception cref="T:System.IO.EndOfStreamException">Thrown if <paramref name="condition"/> is <see langword="false"/>.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.TryGetStringLengthInBytes(System.UInt32@)">
            <summary>
            Gets the length of the next string.
            </summary>
            <param name="length">Receives the length of the next string, if there were enough bytes to read it.</param>
            <returns><see langword="true"/> if there were enough bytes to read the length of the next string; <see langword="false"/> otherwise.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.GetStringLengthInBytes">
            <summary>
            Gets the length of the next string.
            </summary>
            <returns>The length of the next string.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadStringSlow(System.UInt32)">
            <summary>
            Reads a string assuming that it is spread across multiple spans in the <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
            <param name="byteLength">The length of the string to be decoded, in bytes.</param>
            <returns>The decoded string.</returns>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadByte">
            <summary>
            Reads an <see cref="T:System.Byte"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadUInt16">
            <summary>
            Reads an <see cref="T:System.UInt16"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadUInt32">
            <summary>
            Reads an <see cref="T:System.UInt32"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadUInt64">
            <summary>
            Reads an <see cref="T:System.UInt64"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadSByte">
            <summary>
            Reads an <see cref="T:System.SByte"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadInt16">
            <summary>
            Reads an <see cref="T:System.Int16"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadInt32">
            <summary>
            Reads an <see cref="T:System.Int32"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="M:MessagePack.MessagePackReader.ReadInt64">
            <summary>
            Reads an <see cref="T:System.Int64"/> value from:
            Some value between <see cref="F:MessagePack.MessagePackCode.MinNegativeFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxNegativeFixInt"/>,
            Some value between <see cref="F:MessagePack.MessagePackCode.MinFixInt"/> and <see cref="F:MessagePack.MessagePackCode.MaxFixInt"/>,
            or any of the other MsgPack integer types.
            </summary>
            <returns>The value.</returns>
            <exception cref="T:System.OverflowException">Thrown when the value exceeds what can be stored in the returned type.</exception>
        </member>
        <member name="T:MessagePack.MessagePackSecurity">
            <summary>
            Settings related to security, particularly relevant when deserializing data from untrusted sources.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackSecurity.TrustedData">
            <summary>
            Gets an instance preconfigured with settings that omit hash collision resistance protections.
            Useful for deserializing fully-trusted and valid msgpack sequences.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackSecurity.UntrustedData">
            <summary>
            Gets an instance preconfigured with protections applied with reasonable settings for deserializing untrusted msgpack sequences.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.#ctor(MessagePack.MessagePackSecurity)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSecurity"/> class
            with properties copied from a provided template.
            </summary>
            <param name="copyFrom">The template to copy from.</param>
        </member>
        <member name="P:MessagePack.MessagePackSecurity.HashCollisionResistant">
            <summary>
            Gets a value indicating whether data to be deserialized is untrusted and thus should not be allowed to create
            dictionaries or other hash-based collections unless the hashed type has a hash collision resistant implementation available.
            This can mitigate some denial of service attacks when deserializing untrusted code.
            </summary>
            <value>
            The value is <see langword="false"/> for <see cref="F:MessagePack.MessagePackSecurity.TrustedData"/> and <see langword="true"/> for <see cref="F:MessagePack.MessagePackSecurity.UntrustedData"/>.
            </value>
        </member>
        <member name="P:MessagePack.MessagePackSecurity.MaximumObjectGraphDepth">
            <summary>
            Gets the maximum depth of an object graph that may be deserialized.
            </summary>
            <remarks>
            <para>
            This value can be reduced to avoid a stack overflow that would crash the process when deserializing a msgpack sequence designed to cause deep recursion.
            A very short callstack on a thread with 1MB of total stack space might deserialize ~2000 nested arrays before crashing due to a stack overflow.
            Since stack space occupied may vary by the kind of object deserialized, a conservative value for this property to defend against stack overflow attacks might be 500.
            </para>
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.WithMaximumObjectGraphDepth(System.Int32)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSecurity.MaximumObjectGraphDepth"/> property set to a new value.
            </summary>
            <param name="maximumObjectGraphDepth">The new value for the <see cref="P:MessagePack.MessagePackSecurity.MaximumObjectGraphDepth"/> property.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.WithHashCollisionResistant(System.Boolean)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSecurity.HashCollisionResistant"/> property set to a new value.
            </summary>
            <param name="hashCollisionResistant">The new value for the <see cref="P:MessagePack.MessagePackSecurity.HashCollisionResistant"/> property.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.GetEqualityComparer``1">
            <summary>
            Gets an <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> that is suitable to use with a hash-based collection.
            </summary>
            <typeparam name="T">The type of key that will be hashed in the collection.</typeparam>
            <returns>The <see cref="T:System.Collections.Generic.IEqualityComparer`1"/> to use.</returns>
            <remarks>
            When <see cref="P:MessagePack.MessagePackSecurity.HashCollisionResistant"/> is active, this will be a collision resistant instance which may reject certain key types.
            When <see cref="P:MessagePack.MessagePackSecurity.HashCollisionResistant"/> is not active, this will be <see cref="P:System.Collections.Generic.EqualityComparer`1.Default"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.GetEqualityComparer">
            <summary>
            Gets an <see cref="T:System.Collections.IEqualityComparer"/> that is suitable to use with a hash-based collection.
            </summary>
            <returns>The <see cref="T:System.Collections.IEqualityComparer"/> to use.</returns>
            <remarks>
            When <see cref="P:MessagePack.MessagePackSecurity.HashCollisionResistant"/> is active, this will be a collision resistant instance which may reject certain key types.
            When <see cref="P:MessagePack.MessagePackSecurity.HashCollisionResistant"/> is not active, this will be <see cref="P:System.Collections.Generic.EqualityComparer`1.Default"/>.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.GetHashCollisionResistantEqualityComparer``1">
            <summary>
            Returns a hash collision resistant equality comparer.
            </summary>
            <typeparam name="T">The type of key that will be hashed in the collection.</typeparam>
            <returns>A hash collision resistant equality comparer.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.DepthStep(MessagePack.MessagePackReader@)">
            <summary>
            Checks the depth of the deserializing graph and increments it by 1.
            </summary>
            <param name="reader">The reader that is involved in deserialization.</param>
            <remarks>
            Callers should decrement <see cref="P:MessagePack.MessagePackReader.Depth"/> after exiting that edge in the graph.
            </remarks>
            <exception cref="T:System.InsufficientExecutionStackException">Thrown if <see cref="P:MessagePack.MessagePackReader.Depth"/> is already at or exceeds <see cref="P:MessagePack.MessagePackSecurity.MaximumObjectGraphDepth"/>.</exception>
            <remarks>
            Rather than wrap the body of every <see cref="M:MessagePack.Formatters.IMessagePackFormatter`1.Deserialize(MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)"/> method,
            this should wrap *calls* to these methods. They need not appear in pure "thunk" methods that simply delegate the deserialization to another formatter.
            In this way, we can avoid repeatedly incrementing and decrementing the counter when deserializing each element of a collection.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.GetHashCollisionResistantEqualityComparer">
            <summary>
            Returns a hash collision resistant equality comparer.
            </summary>
            <returns>A hash collision resistant equality comparer.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSecurity.Clone">
            <summary>
            Creates a new instance that is a copy of this one.
            </summary>
            <remarks>
            Derived types should override this method to instantiate their own derived type.
            </remarks>
        </member>
        <member name="T:MessagePack.MessagePackSecurity.CollisionResistantHasher`1">
            <summary>
            A hash collision resistant implementation of <see cref="T:System.Collections.Generic.IEqualityComparer`1"/>.
            </summary>
            <typeparam name="T">The type of key that will be hashed.</typeparam>
        </member>
        <member name="T:MessagePack.MessagePackSecurity.ObjectFallbackEqualityComparer">
            <summary>
            A special hash-resistant equality comparer that defers picking the actual implementation
            till it can check the runtime type of each value to be hashed.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackSerializationException">
            <summary>
            An exception thrown during serializing an object graph or deserializing a messagepack sequence.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializationException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSerializationException"/> class.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializationException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSerializationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
        </member>
        <member name="M:MessagePack.MessagePackSerializationException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSerializationException"/> class.
            </summary>
            <param name="message">The exception message.</param>
            <param name="inner">The inner exception.</param>
        </member>
        <member name="M:MessagePack.MessagePackSerializationException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSerializationException"/> class.
            </summary>
            <param name="info">Serialization info.</param>
            <param name="context">Serialization context.</param>
        </member>
        <member name="T:MessagePack.MessagePackSerializer">
            <summary>
            High-Level API of MessagePack for C#.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackSerializer.Typeless">
            <summary>
            A convenience wrapper around <see cref="T:MessagePack.MessagePackSerializer"/> that assumes all generic type arguments are <see cref="T:System.Object"/>
            causing the type of top-level objects to be recorded in the MessagePack stream and thus deserialized to the original type automatically.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackSerializer.Typeless.DefaultOptions">
            <summary>
            Gets or sets the default set of options to use when not explicitly specified for a method call.
            </summary>
            <value>The default value is <see cref="F:MessagePack.Resolvers.TypelessContractlessStandardResolver.Options"/>.</value>
            <remarks>
            This is an AppDomain or process-wide setting.
            If you're writing a library, you should NOT set or rely on this property but should instead pass
            in <see cref="P:MessagePack.MessagePackSerializerOptions.Standard"/> (or the required options) explicitly to every method call
            to guarantee appropriate behavior in any application.
            If you are an app author, realize that setting this property impacts the entire application so it should only be
            set once, and before any use of <see cref="T:MessagePack.MessagePackSerializer"/> occurs.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackSerializer.DefaultOptions">
            <summary>
            Gets or sets the default set of options to use when not explicitly specified for a method call.
            </summary>
            <value>The default value is <see cref="P:MessagePack.MessagePackSerializerOptions.Standard"/>.</value>
            <remarks>
            This is an AppDomain or process-wide setting.
            If you're writing a library, you should NOT set or rely on this property but should instead pass
            in <see cref="P:MessagePack.MessagePackSerializerOptions.Standard"/> (or the required options) explicitly to every method call
            to guarantee appropriate behavior in any application.
            If you are an app author, realize that setting this property impacts the entire application so it should only be
            set once, and before any use of <see cref="T:MessagePack.MessagePackSerializer"/> occurs.
            </remarks>
        </member>
        <member name="F:MessagePack.MessagePackSerializer.scratchArray">
            <summary>
            A thread-local, recyclable array that may be used for short bursts of code.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(System.Buffers.IBufferWriter{System.Byte},``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Serializes a given value with the specified buffer writer.
            </summary>
            <param name="writer">The buffer writer to serialize with.</param>
            <param name="value">The value to serialize.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(MessagePack.MessagePackWriter@,``0,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Serializes a given value with the specified buffer writer.
            </summary>
            <param name="writer">The buffer writer to serialize with.</param>
            <param name="value">The value to serialize.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Serializes a given value with the specified buffer writer.
            </summary>
            <param name="value">The value to serialize.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>A byte array with the serialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize``1(System.IO.Stream,``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Serializes a given value to the specified stream.
            </summary>
            <param name="stream">The stream to serialize to.</param>
            <param name="value">The value to serialize.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeAsync``1(System.IO.Stream,``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Serializes a given value to the specified stream.
            </summary>
            <param name="stream">The stream to serialize to.</param>
            <param name="value">The value to serialize.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>A task that completes with the result of the async serialization operation.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize``1(System.Buffers.ReadOnlySequence{System.Byte}@,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Deserializes a value of a given type from a sequence of bytes.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="byteSequence">The sequence to deserialize from.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize``1(MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Deserializes a value of a given type from a sequence of bytes.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="reader">The reader to deserialize from.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize``1(System.ReadOnlyMemory{System.Byte},MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Deserializes a value of a given type from a sequence of bytes.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="buffer">The buffer to deserialize from.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize``1(System.ReadOnlyMemory{System.Byte},System.Int32@,System.Threading.CancellationToken)">
            <summary>
            Deserializes a value of a given type from a sequence of bytes.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="buffer">The memory to deserialize from.</param>
            <param name="bytesRead">The number of bytes read.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize``1(System.ReadOnlyMemory{System.Byte},MessagePack.MessagePackSerializerOptions,System.Int32@,System.Threading.CancellationToken)">
            <summary>
            Deserializes a value of a given type from a sequence of bytes.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="buffer">The memory to deserialize from.</param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="bytesRead">The number of bytes read.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize``1(System.IO.Stream,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Deserializes the entire content of a <see cref="T:System.IO.Stream"/>.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="stream">
            The stream to deserialize from.
            The entire stream will be read, and the first msgpack token deserialized will be returned.
            If <see cref="P:System.IO.Stream.CanSeek"/> is true on the stream, its position will be set to just after the last deserialized byte.
            </param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
            <remarks>
            If multiple top-level msgpack data structures are expected on the stream, use <see cref="T:MessagePack.MessagePackStreamReader"/> instead.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.DeserializeAsync``1(System.IO.Stream,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Deserializes the entire content of a <see cref="T:System.IO.Stream"/>.
            </summary>
            <typeparam name="T">The type of value to deserialize.</typeparam>
            <param name="stream">
            The stream to deserialize from.
            The entire stream will be read, and the first msgpack token deserialized will be returned.
            If <see cref="P:System.IO.Stream.CanSeek"/> is true on the stream, its position will be set to just after the last deserialized byte.
            </param>
            <param name="options">The options. Use <see langword="null"/> to use default options.</param>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>The deserialized value.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown when any error occurs during deserialization.</exception>
            <remarks>
            If multiple top-level msgpack data structures are expected on the stream, use <see cref="T:MessagePack.MessagePackStreamReader"/> instead.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.LZ4Operation(System.Buffers.ReadOnlySequence{System.Byte}@,System.Span{System.Byte},MessagePack.MessagePackSerializer.LZ4Transform)">
            <summary>
            Performs LZ4 compression or decompression.
            </summary>
            <param name="input">The input for the operation.</param>
            <param name="output">The buffer to write the result of the operation.</param>
            <param name="lz4Operation">The LZ4 codec transformation.</param>
            <returns>The number of bytes written to the <paramref name="output"/>.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeToJson``1(System.IO.TextWriter,``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Serialize an object to JSON string.
            </summary>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if an error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeToJson``1(``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Serialize an object to JSON string.
            </summary>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if an error occurs during serialization.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ConvertToJson(System.ReadOnlyMemory{System.Byte},MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Convert a message-pack binary to a JSON string.
            </summary>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if an error occurs while reading the messagepack data or writing out the JSON.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ConvertToJson(System.Buffers.ReadOnlySequence{System.Byte}@,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Convert a message-pack binary to a JSON string.
            </summary>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if an error occurs while reading the messagepack data or writing out the JSON.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ConvertToJson(MessagePack.MessagePackReader@,System.IO.TextWriter,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Convert a message-pack binary to a JSON string.
            </summary>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if an error occurs while reading the messagepack data or writing out the JSON.</exception>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ConvertFromJson(System.String,MessagePack.MessagePackWriter@,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Translates the given JSON to MessagePack.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ConvertFromJson(System.String,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <summary>
            Translates the given JSON to MessagePack.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.ConvertFromJson(System.IO.TextReader,MessagePack.MessagePackWriter@,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Translates the given JSON to MessagePack.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize(System.Type,MessagePack.MessagePackWriter@,System.Object,MessagePack.MessagePackSerializerOptions)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Serialize``1(MessagePack.MessagePackWriter@,``0,MessagePack.MessagePackSerializerOptions)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize(System.Type,System.Buffers.IBufferWriter{System.Byte},System.Object,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Serialize``1(System.Buffers.IBufferWriter{System.Byte},``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize(System.Type,System.Object,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Serialize``1(``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Serialize(System.Type,System.IO.Stream,System.Object,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Serialize``1(System.IO.Stream,``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeAsync(System.Type,System.IO.Stream,System.Object,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.SerializeAsync``1(System.IO.Stream,``0,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize(System.Type,MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Deserialize``1(MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize(System.Type,System.IO.Stream,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Deserialize``1(System.IO.Stream,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.DeserializeAsync(System.Type,System.IO.Stream,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.DeserializeAsync``1(System.IO.Stream,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize(System.Type,System.ReadOnlyMemory{System.Byte},MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Deserialize``1(System.ReadOnlyMemory{System.Byte},MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.Deserialize(System.Type,System.Buffers.ReadOnlySequence{System.Byte},MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)">
            <seealso cref="M:MessagePack.MessagePackSerializer.Deserialize``1(System.Buffers.ReadOnlySequence{System.Byte}@,MessagePack.MessagePackSerializerOptions,System.Threading.CancellationToken)"/>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.SerializeSemiGeneric``1(MessagePack.MessagePackWriter@,System.Object,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Helper method used by reflection.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializer.DeserializeSemiGeneric``1(MessagePack.MessagePackReader@,MessagePack.MessagePackSerializerOptions)">
            <summary>
            Helper method used by reflection.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackSerializerOptions">
            <summary>
            An immutable description of options for running the <see cref="T:MessagePack.MessagePackSerializer"/>.
            </summary>
        </member>
        <member name="F:MessagePack.MessagePackSerializerOptions.DisallowedTypes">
            <summary>
            A collection of known dangerous types that are not expected in a typical MessagePack stream,
            and thus are rejected by the default implementation of <see cref="M:MessagePack.MessagePackSerializerOptions.ThrowIfDeserializingTypeIsDisallowed(System.Type)"/>.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.Standard">
            <summary>
            Gets a good default set of options that uses the <see cref="T:MessagePack.Resolvers.StandardResolver"/> and no compression.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.#ctor(MessagePack.IFormatterResolver)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSerializerOptions"/> class.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.#ctor(MessagePack.MessagePackSerializerOptions)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackSerializerOptions"/> class
            with members initialized from an existing instance.
            </summary>
            <param name="copyFrom">The options to copy from.</param>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.Resolver">
            <summary>
            Gets the resolver to use for complex types.
            </summary>
            <value>An instance of <see cref="T:MessagePack.IFormatterResolver"/>. Never <see langword="null"/>.</value>
            <exception cref="T:System.ArgumentNullException">Thrown if an attempt is made to set this property to <see langword="null"/>.</exception>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.Compression">
            <summary>
            Gets the compression scheme to apply to serialized sequences.
            </summary>
            <remarks>
            When set to something other than <see cref="F:MessagePack.MessagePackCompression.None"/>,
            deserialization can still work on uncompressed sequences,
            and serialization may not compress if msgpack sequences are short enough that compression would not likely be advantageous.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.CompressionMinLength">
            <summary>
            Gets the length a serialized msgpack result must equal or exceed before <see cref="P:MessagePack.MessagePackSerializerOptions.Compression"/> is applied.
            </summary>
            <value>The default value is 64.</value>
            <remarks>
            When compression is <em>not</em> applied due to a short serialized result, deserialization will still succeed
            even if <see cref="P:MessagePack.MessagePackSerializerOptions.Compression"/> is set to something other than <see cref="F:MessagePack.MessagePackCompression.None"/>.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.SuggestedContiguousMemorySize">
            <summary>
            Gets the size of contiguous memory blocks in bytes that may be allocated for buffering purposes.
            </summary>
            <value>The default value is 1MB.</value>
            <remarks>
            Larger values may perform a bit faster, but may result in adding a runtime perf tax due to using the
            <see href="https://docs.microsoft.com/en-us/dotnet/standard/garbage-collection/large-object-heap">Large Object Heap</see>.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.OldSpec">
            <summary>
            Gets a value indicating whether to serialize with <see cref="P:MessagePack.MessagePackWriter.OldSpec"/> set to some value
            causing messagepack spec compliance to be explicitly set to the old or new format.
            </summary>
            <value>
            A null value means the <see cref="T:MessagePack.MessagePackWriter"/>'s default or previous setting will be used.
            A non-null value will be applied to the <see cref="P:MessagePack.MessagePackWriter.OldSpec"/> property for the duration of a
            serialization and then reverted to its prior setting.
            </value>
            <remarks>
            Reading always supports both new and old spec.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.OmitAssemblyVersion">
            <summary>
            Gets a value indicating whether serialization should omit assembly version, culture and public key token metadata when using the typeless formatter.
            </summary>
            <value>The default value is <see langword="false"/>.</value>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.AllowAssemblyVersionMismatch">
            <summary>
            Gets a value indicating whether deserialization may instantiate types from an assembly with a different version if a matching version cannot be found.
            </summary>
            <value>The default value is <see langword="false"/>.</value>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.Security">
            <summary>
            Gets the security-related options for deserializing messagepack sequences.
            </summary>
            <value>
            The default value is to use <see cref="F:MessagePack.MessagePackSecurity.TrustedData"/>.
            </value>
        </member>
        <member name="P:MessagePack.MessagePackSerializerOptions.SequencePool">
            <summary>
            Gets a thread-safe pool of reusable <see cref="T:Nerdbank.Streams.Sequence`1"/> objects.
            </summary>
            <value>The default value is the <see cref="F:MessagePack.SequencePool.Shared"/> instance.</value>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.LoadType(System.String)">
            <summary>
            Gets a type given a string representation of the type.
            </summary>
            <param name="typeName">The name of the type to load. This is typically the <see cref="P:System.Type.AssemblyQualifiedName"/> but may use the assembly's simple name.</param>
            <returns>The loaded type or <see langword="null"/> if no matching type could be found.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.ThrowIfDeserializingTypeIsDisallowed(System.Type)">
            <summary>
            Checks whether a given type may be deserialized.
            </summary>
            <param name="type">The type to be instantiated.</param>
            <exception cref="T:System.TypeAccessException">Thrown if the <paramref name="type"/> is not allowed to be deserialized.</exception>
            <remarks>
            This method provides a means for an important security mitigation when using the Typeless formatter to prevent untrusted messagepack from
            deserializing objects that may be harmful if instantiated, disposed or finalized.
            The default implementation throws for only a few known dangerous types.
            Applications that deserialize from untrusted sources should override this method and throw if the type is not among the expected set.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithResolver(MessagePack.IFormatterResolver)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.Resolver"/> property set to a new value.
            </summary>
            <param name="resolver">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.Resolver"/>.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithCompression(MessagePack.MessagePackCompression)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.Compression"/> property set to a new value.
            </summary>
            <param name="compression">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.Compression"/> property.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithCompressionMinLength(System.Int32)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.CompressionMinLength"/> property set to a new value.
            </summary>
            <param name="compressionMinLength">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.CompressionMinLength"/> property. Must be a positive integer.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithSuggestedContiguousMemorySize(System.Int32)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.SuggestedContiguousMemorySize"/> property set to a new value.
            </summary>
            <param name="suggestedContiguousMemorySize">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.SuggestedContiguousMemorySize"/> property. Must be at least 256.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithOldSpec(System.Nullable{System.Boolean})">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.OldSpec"/> property set to a new value.
            </summary>
            <param name="oldSpec">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.OldSpec"/>.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithOmitAssemblyVersion(System.Boolean)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.OmitAssemblyVersion"/> property set to a new value.
            </summary>
            <param name="omitAssemblyVersion">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.OmitAssemblyVersion"/> property.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithAllowAssemblyVersionMismatch(System.Boolean)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.AllowAssemblyVersionMismatch"/> property set to a new value.
            </summary>
            <param name="allowAssemblyVersionMismatch">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.AllowAssemblyVersionMismatch"/> property.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithSecurity(MessagePack.MessagePackSecurity)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.Security"/> property set to a new value.
            </summary>
            <param name="security">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.Security"/> property.</param>
            <returns>The new instance; or the original if the value is unchanged.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.WithPool(MessagePack.SequencePool)">
            <summary>
            Gets a copy of these options with the <see cref="P:MessagePack.MessagePackSerializerOptions.SequencePool"/> property set to a new value.
            </summary>
            <param name="pool">The new value for the <see cref="P:MessagePack.MessagePackSerializerOptions.SequencePool"/> property.</param>
            <returns>The new instance.</returns>
        </member>
        <member name="M:MessagePack.MessagePackSerializerOptions.Clone">
            <summary>
            Creates a clone of this instance with the same properties set.
            </summary>
            <returns>The cloned instance. Guaranteed to be a new instance.</returns>
            <exception cref="T:System.NotSupportedException">Thrown if this instance is a derived type that doesn't override this method.</exception>
        </member>
        <member name="T:MessagePack.MessagePackStreamReader">
            <summary>
            Reads one or more messagepack data structures from a <see cref="T:System.IO.Stream"/>.
            </summary>
            <remarks>
            This class is *not* thread-safe. Do not call more than one member at once and be sure any call completes (including asynchronous tasks)
            before calling the next one.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.ReadArrayHeaderAsync(System.Threading.CancellationToken)">
            <summary>
            Reads the next messagepack array header.
            </summary>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>
            A task whose result is the length of the next array from the stream.
            </returns>
            <remarks>
            Any extra bytes read (between the last complete message and the end of the stream) will be available via the <see cref="P:MessagePack.MessagePackStreamReader.RemainingBytes"/> property.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.ReadArrayAsync(System.Threading.CancellationToken)">
            <summary>
            Reads the next messagepack array and produces each element individually.
            </summary>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>
            A task whose result is the next whole data structure from the stream, or <see langword="null"/> if the stream ends.
            The returned sequence is valid until this <see cref="T:MessagePack.MessagePackStreamReader"/> is disposed or
            until this method is called again, whichever comes first.
            </returns>
            <remarks>
            When <see langword="null"/> is the result of the returned task,
            any extra bytes read (between the last complete message and the end of the stream) will be available via the <see cref="P:MessagePack.MessagePackStreamReader.RemainingBytes"/> property.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.TryReadArrayHeader(System.Int32@)">
            <summary>
            Reads an array header from <see cref="P:MessagePack.MessagePackStreamReader.ReadData"/> if there are enough bytes to do so.
            </summary>
            <param name="length">Receives the length of the array, if its header could be read.</param>
            <returns><see langword="true"/> if the array header was found and complete; <see langword="false"/> if there were insufficient bytes to read the header.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if the next msgpack structure is not an array header.</exception>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackStreamReader"/> class.
            </summary>
            <param name="stream">The stream to read from. This stream will be disposed of when this <see cref="T:MessagePack.MessagePackStreamReader"/> is disposed.</param>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackStreamReader"/> class.
            </summary>
            <param name="stream">The stream to read from.</param>
            <param name="leaveOpen">If true, leaves the stream open after this <see cref="T:MessagePack.MessagePackStreamReader"/> is disposed; otherwise, false.</param>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.#ctor(System.IO.Stream,System.Boolean,MessagePack.SequencePool)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackStreamReader"/> class.
            </summary>
            <param name="stream">The stream to read from.</param>
            <param name="leaveOpen">If true, leaves the stream open after this <see cref="T:MessagePack.MessagePackStreamReader"/> is disposed; otherwise, false.</param>
            <param name="sequencePool">The pool to rent a <see cref="T:Nerdbank.Streams.Sequence`1"/> object from.</param>
        </member>
        <member name="P:MessagePack.MessagePackStreamReader.RemainingBytes">
            <summary>
            Gets any bytes that have been read since the last complete message returned from <see cref="M:MessagePack.MessagePackStreamReader.ReadAsync(System.Threading.CancellationToken)"/>.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackStreamReader.ReadData">
            <summary>
            Gets the sequence that we read data from the <see cref="F:MessagePack.MessagePackStreamReader.stream"/> into.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.ReadAsync(System.Threading.CancellationToken)">
            <summary>
            Reads the next whole (top-level) messagepack data structure.
            </summary>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>
            A task whose result is the next whole data structure from the stream, or <see langword="null"/> if the stream ends.
            The returned sequence is valid until this <see cref="T:MessagePack.MessagePackStreamReader"/> is disposed or
            until this method is called again, whichever comes first.
            </returns>
            <remarks>
            When <see langword="null"/> is the result of the returned task,
            any extra bytes read (between the last complete message and the end of the stream) will be available via the <see cref="P:MessagePack.MessagePackStreamReader.RemainingBytes"/> property.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.DiscardBufferedData">
            <summary>
            Arranges for the next read operation to start by reading from the underlying <see cref="T:System.IO.Stream"/>
            instead of any data buffered from a previous read.
            </summary>
            <remarks>
            This is appropriate if the underlying <see cref="T:System.IO.Stream"/> has been repositioned such that
            any previously buffered data is no longer applicable to what the caller wants to read.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.Dispose">
            <inheritdoc/>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.Dispose(System.Boolean)">
            <summary>
            Disposes of managed and unmanaged resources.
            </summary>
            <param name="disposing"><see langword="true"/> if this instance is being disposed; <see langword="false"/> if it is being finalized.</param>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.RecycleLastMessage">
            <summary>
            Recycle memory from a previously returned message.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.TryReadMoreDataAsync(System.Threading.CancellationToken)">
            <summary>
            Read more data from the stream into the <see cref="P:MessagePack.MessagePackStreamReader.ReadData"/> buffer.
            </summary>
            <param name="cancellationToken">A cancellation token.</param>
            <returns><see langword="true"/> if more data was read; <see langword="false"/> if the end of the stream had already been reached.</returns>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.TryReadNextMessage(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Checks whether the content in <see cref="P:MessagePack.MessagePackStreamReader.ReadData"/> include a complete messagepack structure.
            </summary>
            <param name="completeMessage">Receives the sequence of the first complete data structure found, if any.</param>
            <returns><see langword="true"/> if a complete data structure was found; <see langword="false"/> otherwise.</returns>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.ReadMapHeaderAsync(System.Threading.CancellationToken)">
            <summary>
            Reads the next messagepack map header.
            </summary>
            <param name="cancellationToken">A cancellation token.</param>
            <returns>
            A task whose result is the size of the next map from the stream.
            </returns>
            <remarks>
            Any extra bytes read (between the last complete message and the end of the stream) will be available via the <see cref="P:MessagePack.MessagePackStreamReader.RemainingBytes"/> property.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackStreamReader.TryReadMapHeader(System.Int32@)">
            <summary>
            Reads a map header from <see cref="P:MessagePack.MessagePackStreamReader.ReadData"/> if there are enough bytes to do so.
            </summary>
            <param name="count">Receives the size of the map, if its header could be read.</param>
            <returns><see langword="true"/> if the map header was found and complete; <see langword="false"/> if there were insufficient bytes to read the header.</returns>
            <exception cref="T:MessagePack.MessagePackSerializationException">Thrown if the next msgpack structure is not a map header.</exception>
        </member>
        <member name="T:MessagePack.MessagePackWriter">
            <summary>
            A primitive types writer for the MessagePack format.
            </summary>
            <remarks>
            <see href="https://github.com/msgpack/msgpack/blob/master/spec.md">The MessagePack spec.</see>.
            </remarks>
        </member>
        <member name="F:MessagePack.MessagePackWriter.writer">
            <summary>
            The writer to use.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackWriter.#ctor(System.Buffers.IBufferWriter{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackWriter"/> struct.
            </summary>
            <param name="writer">The writer to use.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.#ctor(MessagePack.SequencePool,System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackWriter"/> struct.
            </summary>
            <param name="sequencePool">The pool from which to draw an <see cref="T:System.Buffers.IBufferWriter`1"/> if required..</param>
            <param name="array">An array to start with so we can avoid accessing the <paramref name="sequencePool"/> if possible.</param>
        </member>
        <member name="P:MessagePack.MessagePackWriter.CancellationToken">
            <summary>
            Gets or sets the cancellation token for this serialization operation.
            </summary>
        </member>
        <member name="P:MessagePack.MessagePackWriter.OldSpec">
            <summary>
            Gets or sets a value indicating whether to write in <see href="https://github.com/msgpack/msgpack/blob/master/spec-old.md">old spec</see> compatibility mode.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Clone(System.Buffers.IBufferWriter{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackWriter"/> struct,
            with the same settings as this one, but with its own buffer writer.
            </summary>
            <param name="writer">The writer to use for the new instance.</param>
            <returns>The new writer.</returns>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Flush">
            <summary>
            Ensures everything previously written has been flushed to the underlying <see cref="T:System.Buffers.IBufferWriter`1"/>.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteNil">
            <summary>
            Writes a <see cref="F:MessagePack.MessagePackCode.Nil"/> value.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteRaw(System.ReadOnlySpan{System.Byte})">
            <summary>
            Copies bytes directly into the message pack writer.
            </summary>
            <param name="rawMessagePackBlock">The span of bytes to copy from.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteRaw(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Copies bytes directly into the message pack writer.
            </summary>
            <param name="rawMessagePackBlock">The span of bytes to copy from.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteArrayHeader(System.Int32)">
            <summary>
            Write the length of the next array to be written in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixArray"/>,
            <see cref="F:MessagePack.MessagePackCode.Array16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Array32"/>.
            </summary>
            <param name="count">The number of elements that will be written in the array.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteArrayHeader(System.UInt32)">
            <summary>
            Write the length of the next array to be written in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixArray"/>,
            <see cref="F:MessagePack.MessagePackCode.Array16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Array32"/>.
            </summary>
            <param name="count">The number of elements that will be written in the array.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteMapHeader(System.Int32)">
            <summary>
            Write the length of the next map to be written in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixMap"/>,
            <see cref="F:MessagePack.MessagePackCode.Map16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Map32"/>.
            </summary>
            <param name="count">The number of key=value pairs that will be written in the map.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteMapHeader(System.UInt32)">
            <summary>
            Write the length of the next map to be written in the most compact form of
            <see cref="F:MessagePack.MessagePackCode.MinFixMap"/>,
            <see cref="F:MessagePack.MessagePackCode.Map16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Map32"/>.
            </summary>
            <param name="count">The number of key=value pairs that will be written in the map.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value using a 1-byte code when possible, otherwise as <see cref="F:MessagePack.MessagePackCode.UInt8"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteUInt8(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value using <see cref="F:MessagePack.MessagePackCode.UInt8"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.SByte)">
            <summary>
            Writes an 8-bit value using a 1-byte code when possible, otherwise as <see cref="F:MessagePack.MessagePackCode.Int8"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteInt8(System.SByte)">
            <summary>
            Writes an 8-bit value using <see cref="F:MessagePack.MessagePackCode.Int8"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.UInt16)">
            <summary>
            Writes a <see cref="T:System.UInt16"/> value using a 1-byte code when possible, otherwise as <see cref="F:MessagePack.MessagePackCode.UInt8"/> or <see cref="F:MessagePack.MessagePackCode.UInt16"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteUInt16(System.UInt16)">
            <summary>
            Writes a <see cref="T:System.UInt16"/> value using <see cref="F:MessagePack.MessagePackCode.UInt16"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> using a built-in 1-byte code when within specific MessagePack-supported ranges,
            or the most compact of
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>, or
            <see cref="F:MessagePack.MessagePackCode.Int16"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteInt16(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> using <see cref="F:MessagePack.MessagePackCode.Int16"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.UInt32)">
            <summary>
            Writes an <see cref="T:System.UInt32"/> using a built-in 1-byte code when within specific MessagePack-supported ranges,
            or the most compact of
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>, or
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteUInt32(System.UInt32)">
            <summary>
            Writes an <see cref="T:System.UInt32"/> using <see cref="F:MessagePack.MessagePackCode.UInt32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Int32)">
            <summary>
            Writes an <see cref="T:System.Int32"/> using a built-in 1-byte code when within specific MessagePack-supported ranges,
            or the most compact of
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteInt32(System.Int32)">
            <summary>
            Writes an <see cref="T:System.Int32"/> using <see cref="F:MessagePack.MessagePackCode.Int32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.UInt64)">
            <summary>
            Writes an <see cref="T:System.UInt64"/> using a built-in 1-byte code when within specific MessagePack-supported ranges,
            or the most compact of
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteUInt64(System.UInt64)">
            <summary>
            Writes an <see cref="T:System.UInt64"/> using <see cref="F:MessagePack.MessagePackCode.Int32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Int64)">
            <summary>
            Writes an <see cref="T:System.Int64"/> using a built-in 1-byte code when within specific MessagePack-supported ranges,
            or the most compact of
            <see cref="F:MessagePack.MessagePackCode.UInt8"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt16"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt32"/>,
            <see cref="F:MessagePack.MessagePackCode.UInt64"/>,
            <see cref="F:MessagePack.MessagePackCode.Int8"/>,
            <see cref="F:MessagePack.MessagePackCode.Int16"/>,
            <see cref="F:MessagePack.MessagePackCode.Int32"/>,
            <see cref="F:MessagePack.MessagePackCode.Int64"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteInt64(System.Int64)">
            <summary>
            Writes a <see cref="T:System.Int64"/> using <see cref="F:MessagePack.MessagePackCode.Int64"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Boolean)">
            <summary>
            Writes a <see cref="T:System.Boolean"/> value using either <see cref="F:MessagePack.MessagePackCode.True"/> or <see cref="F:MessagePack.MessagePackCode.False"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Char)">
            <summary>
            Writes a <see cref="T:System.Char"/> value using a 1-byte code when possible, otherwise as <see cref="F:MessagePack.MessagePackCode.UInt8"/> or <see cref="F:MessagePack.MessagePackCode.UInt16"/>.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Single)">
            <summary>
            Writes a <see cref="F:MessagePack.MessagePackCode.Float32"/> value.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Double)">
            <summary>
            Writes a <see cref="F:MessagePack.MessagePackCode.Float64"/> value.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.DateTime)">
            <summary>
            Writes a <see cref="T:System.DateTime"/> using the message code <see cref="F:MessagePack.ReservedMessagePackExtensionTypeCode.DateTime"/>.
            </summary>
            <param name="dateTime">The value to write.</param>
            <exception cref="T:System.NotSupportedException">Thrown when <see cref="P:MessagePack.MessagePackWriter.OldSpec"/> is true because the old spec does not define a <see cref="T:System.DateTime"/> format.</exception>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Byte[])">
            <summary>
            Writes a <see cref="T:System.Byte"/>[], prefixed with a length encoded as the smallest fitting from:
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>,
            or <see cref="F:MessagePack.MessagePackCode.Nil"/> if <paramref name="src"/> is <see langword="null"/>.
            </summary>
            <param name="src">The array of bytes to write. May be <see langword="null"/>.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.ReadOnlySpan{System.Byte})">
            <summary>
            Writes a span of bytes, prefixed with a length encoded as the smallest fitting from:
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>.
            </summary>
            <param name="src">The span of bytes to write.</param>
            <remarks>
            When <see cref="P:MessagePack.MessagePackWriter.OldSpec"/> is <see langword="true"/>, the msgpack code used is <see cref="F:MessagePack.MessagePackCode.Str8"/>, <see cref="F:MessagePack.MessagePackCode.Str16"/> or <see cref="F:MessagePack.MessagePackCode.Str32"/> instead.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Writes a sequence of bytes, prefixed with a length encoded as the smallest fitting from:
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>.
            </summary>
            <param name="src">The span of bytes to write.</param>
            <remarks>
            When <see cref="P:MessagePack.MessagePackWriter.OldSpec"/> is <see langword="true"/>, the msgpack code used is <see cref="F:MessagePack.MessagePackCode.Str8"/>, <see cref="F:MessagePack.MessagePackCode.Str16"/> or <see cref="F:MessagePack.MessagePackCode.Str32"/> instead.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteBinHeader(System.Int32)">
            <summary>
            Writes the header that precedes a raw binary sequence with a length encoded as the smallest fitting from:
            <see cref="F:MessagePack.MessagePackCode.Bin8"/>,
            <see cref="F:MessagePack.MessagePackCode.Bin16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Bin32"/>.
            </summary>
            <param name="length">The length of bytes that will be written next.</param>
            <remarks>
            <para>
            The caller should use <see cref="M:MessagePack.MessagePackWriter.WriteRaw(System.Buffers.ReadOnlySequence{System.Byte}@)"/> or <see cref="M:MessagePack.MessagePackWriter.WriteRaw(System.ReadOnlySpan{System.Byte})"/>
            after calling this method to actually write the content.
            Alternatively a single call to <see cref="M:MessagePack.MessagePackWriter.Write(System.ReadOnlySpan{System.Byte})"/> or <see cref="M:MessagePack.MessagePackWriter.Write(System.Buffers.ReadOnlySequence{System.Byte}@)"/> will take care of the header and content in one call.
            </para>
            <para>
            When <see cref="P:MessagePack.MessagePackWriter.OldSpec"/> is <see langword="true"/>, the msgpack code used is <see cref="F:MessagePack.MessagePackCode.Str8"/>, <see cref="F:MessagePack.MessagePackCode.Str16"/> or <see cref="F:MessagePack.MessagePackCode.Str32"/> instead.
            </para>
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteString(System.Buffers.ReadOnlySequence{System.Byte}@)">
            <summary>
            Writes out an array of bytes that (may) represent a UTF-8 encoded string, prefixed with the length using one of these message codes:
            <see cref="F:MessagePack.MessagePackCode.MinFixStr"/>,
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Str32"/>.
            </summary>
            <param name="utf8stringBytes">The bytes to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteString(System.ReadOnlySpan{System.Byte})">
            <summary>
            Writes out an array of bytes that (may) represent a UTF-8 encoded string, prefixed with the length using one of these message codes:
            <see cref="F:MessagePack.MessagePackCode.MinFixStr"/>,
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Str32"/>.
            </summary>
            <param name="utf8stringBytes">The bytes to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteStringHeader(System.Int32)">
            <summary>
            Writes out the header that may precede a UTF-8 encoded string, prefixed with the length using one of these message codes:
            <see cref="F:MessagePack.MessagePackCode.MinFixStr"/>,
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Str32"/>.
            </summary>
            <param name="byteCount">The number of bytes in the string that will follow this header.</param>
            <remarks>
            The caller should use <see cref="M:MessagePack.MessagePackWriter.WriteRaw(System.Buffers.ReadOnlySequence{System.Byte}@)"/> or <see cref="M:MessagePack.MessagePackWriter.WriteRaw(System.ReadOnlySpan{System.Byte})"/>
            after calling this method to actually write the content.
            Alternatively a single call to <see cref="M:MessagePack.MessagePackWriter.WriteString(System.ReadOnlySpan{System.Byte})"/> or <see cref="M:MessagePack.MessagePackWriter.WriteString(System.Buffers.ReadOnlySequence{System.Byte}@)"/> will take care of the header and content in one call.
            </remarks>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.String)">
            <summary>
            Writes out a <see cref="T:System.String"/>, prefixed with the length using one of these message codes:
            <see cref="F:MessagePack.MessagePackCode.MinFixStr"/>,
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>,
            or <see cref="F:MessagePack.MessagePackCode.Nil"/> if the <paramref name="value"/> is <see langword="null"/>.
            </summary>
            <param name="value">The value to write. May be null.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Write(System.ReadOnlySpan{System.Char})">
            <summary>
            Writes out a <see cref="T:System.String"/>, prefixed with the length using one of these message codes:
            <see cref="F:MessagePack.MessagePackCode.MinFixStr"/>,
            <see cref="F:MessagePack.MessagePackCode.Str8"/>,
            <see cref="F:MessagePack.MessagePackCode.Str16"/>,
            <see cref="F:MessagePack.MessagePackCode.Str32"/>.
            </summary>
            <param name="value">The value to write.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteExtensionFormatHeader(MessagePack.ExtensionHeader)">
            <summary>
            Writes the extension format header, using the smallest one of these codes:
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>.
            </summary>
            <param name="extensionHeader">The extension header.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteExtensionFormat(MessagePack.ExtensionResult)">
            <summary>
            Writes an extension format, using the smallest one of these codes:
            <see cref="F:MessagePack.MessagePackCode.FixExt1"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt2"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt4"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt8"/>,
            <see cref="F:MessagePack.MessagePackCode.FixExt16"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext8"/>,
            <see cref="F:MessagePack.MessagePackCode.Ext16"/>, or
            <see cref="F:MessagePack.MessagePackCode.Ext32"/>.
            </summary>
            <param name="extensionData">The extension data.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.GetSpan(System.Int32)">
            <summary>
            Gets memory where raw messagepack data can be written.
            </summary>
            <param name="length">The minimum length of the returned System.Span`1. If 0, a non-empty buffer is returned.</param>
            <returns>The span of memory to write to. This *may* exceed <paramref name="length"/>.</returns>
            <remarks>
            <para>After initializing the resulting memory, always follow up with a call to <see cref="M:MessagePack.MessagePackWriter.Advance(System.Int32)"/>.</para>
            <para>
            This is similar in purpose to <see cref="M:MessagePack.MessagePackWriter.WriteRaw(System.ReadOnlySpan{System.Byte})"/>
            but provides uninitialized memory for the caller to write to instead of copying initialized memory from elsewhere.
            </para>
            </remarks>
            <seealso cref="M:System.Buffers.IBufferWriter`1.GetSpan(System.Int32)"/>
        </member>
        <member name="M:MessagePack.MessagePackWriter.Advance(System.Int32)">
            <summary>
            Commits memory previously returned from <see cref="M:MessagePack.MessagePackWriter.GetSpan(System.Int32)"/> as initialized.
            </summary>
            <param name="length">The number of bytes initialized with messagepack data from the previously returned span.</param>
            <seealso cref="M:System.Buffers.IBufferWriter`1.Advance(System.Int32)"/>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteString_PrepareSpan(System.Int32,System.Int32@,System.Int32@)">
            <summary>
            Estimates the length of the header required for a given string.
            </summary>
            <param name="characterLength">The length of the string to be written, in characters.</param>
            <param name="bufferSize">Receives the guaranteed length of the returned buffer.</param>
            <param name="encodedBytesOffset">Receives the offset within the returned buffer to write the encoded string to.</param>
            <returns>
            A reference to the first byte in the buffer.
            </returns>
        </member>
        <member name="M:MessagePack.MessagePackWriter.WriteString_PostEncoding(System.Byte*,System.Int32,System.Int32)">
            <summary>
            Finalizes an encoding of a string.
            </summary>
            <param name="pBuffer">A pointer obtained from a prior call to <see cref="M:MessagePack.MessagePackWriter.WriteString_PrepareSpan(System.Int32,System.Int32@,System.Int32@)"/>.</param>
            <param name="estimatedOffset">The offset obtained from a prior call to <see cref="M:MessagePack.MessagePackWriter.WriteString_PrepareSpan(System.Int32,System.Int32@,System.Int32@)"/>.</param>
            <param name="byteCount">The number of bytes used to actually encode the string.</param>
        </member>
        <member name="M:MessagePack.MessagePackWriter.GetEncodedLength(System.Int64)">
            <summary>
            Get the number of bytes required to encode a value in msgpack.
            </summary>
            <param name="value">The value to encode.</param>
            <returns>The byte length; One of 1, 2, 3, 5 or 9 bytes.</returns>
        </member>
        <member name="M:MessagePack.MessagePackWriter.GetEncodedLength(System.UInt64)">
            <summary>
            Get the number of bytes required to encode a value in msgpack.
            </summary>
            <param name="value">The value to encode.</param>
            <returns>The byte length; One of 1, 2, 3, 5 or 9 bytes.</returns>
        </member>
        <member name="T:MessagePack.MonoProtection">
            <summary>
            Special behavior for running on the mono runtime.
            </summary>
        </member>
        <member name="P:MessagePack.MonoProtection.IsRunningOnMono">
            <summary>
            Gets a value indicating whether the mono runtime is executing this code.
            </summary>
        </member>
        <member name="F:MessagePack.MonoProtection.RefEmitLock">
            <summary>
            A lock that we enter on mono when generating dynamic types.
            </summary>
        </member>
        <member name="M:MessagePack.MonoProtection.EnterRefEmitLock">
            <summary>
            The method to call within the expression of a <c>using</c> statement whose block surrounds all Ref.Emit code.
            </summary>
            <returns>The value to be disposed of to exit the Ref.Emit lock.</returns>
            <remarks>
            This is a no-op except when running on Mono.
            <see href="https://github.com/mono/mono/issues/20369#issuecomment-690316456">Mono's implementation of Ref.Emit is not thread-safe</see> so we have to lock around all use of it
            when using that runtime.
            </remarks>
        </member>
        <member name="T:MessagePack.ReservedExtensionTypeCodes">
            <summary>
            The extension type codes that this library defines for just this library.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityVector2">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityVector3">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityVector4">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityQuaternion">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityColor">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityBounds">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityRect">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityInt">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityFloat">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.UnityDouble">
            <summary>
            For Unity's UnsafeBlitFormatter.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.Lz4BlockArray">
            <summary>
            The LZ4 array block compression extension.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.Lz4Block">
            <summary>
            The LZ4 single block compression extension.
            </summary>
        </member>
        <member name="F:MessagePack.ReservedExtensionTypeCodes.TypelessFormatter">
            <summary>
            For the <see cref="T:MessagePack.Formatters.TypelessFormatter"/>.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.AttributeFormatterResolver">
            <summary>
            Get formatter from <see cref="T:MessagePack.MessagePackFormatterAttribute"/>.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.AttributeFormatterResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.BuiltinResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.CachingFormatterResolver">
            <summary>
            A base class for <see cref="T:MessagePack.IFormatterResolver"/> classes that want to cache their responses for perf reasons.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.CachingFormatterResolver.formatters">
            <summary>
            The cache of types to their formatters.
            </summary>
        </member>
        <member name="M:MessagePack.Resolvers.CachingFormatterResolver.GetFormatter``1">
            <inheritdoc />
        </member>
        <member name="M:MessagePack.Resolvers.CachingFormatterResolver.GetFormatterCore``1">
            <summary>
            Looks up a formatter for a type that has not been previously cached.
            </summary>
            <typeparam name="T">The type to be formatted.</typeparam>
            <returns>The formatter to use, or <see langword="null"/> if none found.</returns>
        </member>
        <member name="T:MessagePack.Resolvers.CompositeResolver">
            <summary>
            Represents a collection of formatters and resolvers acting as one.
            </summary>
            <remarks>
            This class is not thread-safe for mutations. It is thread-safe when not being written to.
            </remarks>
        </member>
        <member name="M:MessagePack.Resolvers.CompositeResolver.Create(System.Collections.Generic.IReadOnlyList{MessagePack.Formatters.IMessagePackFormatter},System.Collections.Generic.IReadOnlyList{MessagePack.IFormatterResolver})">
            <summary>
            Initializes a new instance of an <see cref="T:MessagePack.IFormatterResolver"/> with the specified formatters and sub-resolvers.
            </summary>
            <param name="formatters">
            A list of instances of <see cref="T:MessagePack.Formatters.IMessagePackFormatter`1"/> to prefer (above the <paramref name="resolvers"/>).
            The formatters are searched in the order given, so if two formatters support serializing the same type, the first one is used.
            May not be null, but may be <see cref="M:System.Array.Empty``1"/>.
            </param>
            <param name="resolvers">
            A list of resolvers to use for serializing types for which <paramref name="formatters"/> does not include a formatter.
            The resolvers are searched in the order given, so if two resolvers support serializing the same type, the first one is used.
            May not be null, but may be <see cref="M:System.Array.Empty``1"/>.
            </param>
            <returns>
            An instance of <see cref="T:MessagePack.IFormatterResolver"/>.
            </returns>
        </member>
        <member name="M:MessagePack.Resolvers.CompositeResolver.CachingResolver.#ctor(MessagePack.Formatters.IMessagePackFormatter[],MessagePack.IFormatterResolver[])">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.Resolvers.CompositeResolver.CachingResolver"/> class.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicEnumAsStringIgnoreCaseResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicEnumAsStringResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicEnumAsStringResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicEnumResolver">
            <summary>
            EnumResolver by dynamic code generation, serialized underlying type.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicEnumResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicGenericResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicObjectResolver">
            <summary>
            ObjectResolver by dynamic code generation.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicObjectResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicObjectResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicObjectResolverAllowPrivate">
            <summary>
            ObjectResolver by dynamic code generation, allow private member.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicContractlessObjectResolver">
            <summary>
            ObjectResolver by dynamic code generation, no needs MessagePackObject attribute and serialized key as string.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicContractlessObjectResolverAllowPrivate">
            <summary>
            ObjectResolver by dynamic code generation, no needs MessagePackObject attribute and serialized key as string, allow private member.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.DynamicUnionResolver">
            <summary>
            UnionResolver by dynamic code generation.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicUnionResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.DynamicUnionResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.ExpandoObjectResolver">
            <summary>
            A resolver for use when deserializing MessagePack data where the schema is not known at compile-time
            such that strong-types can be instantiated.
            Instead, <see cref="T:System.Dynamic.ExpandoObject"/> is used wherever a MessagePack <em>map</em> is encountered.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.ExpandoObjectResolver.Instance">
            <summary>
            The resolver to use to deserialize into C#'s <c>dynamic</c> keyword.
            </summary>
            <remarks>
            This resolver includes more than just the <see cref="T:MessagePack.Formatters.ExpandoObjectFormatter"/>.
            </remarks>
        </member>
        <member name="F:MessagePack.Resolvers.ExpandoObjectResolver.Options">
            <summary>
            A set of options that includes the <see cref="F:MessagePack.Resolvers.ExpandoObjectResolver.Instance"/>
            and puts the deserializer into <see cref="F:MessagePack.MessagePackSecurity.UntrustedData"/> mode.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.NativeDateTimeResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.NativeDateTimeResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.NativeDecimalResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.NativeGuidResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.PrimitiveObjectResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.PrimitiveObjectResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.SourceGeneratedFormatterResolver">
            <summary>
            A resolver that discovers formatters generated by <c>MessagePack.SourceGenerator</c>.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.SourceGeneratedFormatterResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="M:MessagePack.Resolvers.SourceGeneratedFormatterResolver.GetFormatter``1">
            <inheritdoc/>
        </member>
        <member name="T:MessagePack.Resolvers.StandardResolver">
            <summary>
            Default composited resolver, builtin -> attribute -> dynamic enum -> dynamic generic -> dynamic union -> dynamic object -> primitive.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.StandardResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.StandardResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this resolver pre-configured.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.ContractlessStandardResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.ContractlessStandardResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this resolver pre-configured.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.StandardResolverAllowPrivate.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.StandardResolverAllowPrivate.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this resolver pre-configured.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.ContractlessStandardResolverAllowPrivate.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.ContractlessStandardResolverAllowPrivate.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this resolver pre-configured.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.StaticCompositeResolver">
            <summary>
            Singleton version of <see cref="T:MessagePack.Resolvers.CompositeResolver"/>, which can register a collection of formatters and resolvers to a single instance.
            </summary>
        </member>
        <member name="M:MessagePack.Resolvers.StaticCompositeResolver.Register(MessagePack.Formatters.IMessagePackFormatter[])">
            <summary>
            Initializes a singleton instance with the specified formatters.
            This method can only call before use StaticCompositeResolver.Instance.GetFormatter.
            If call twice in the Register methods, registered formatters and resolvers will be overridden.
            </summary>
            <param name="formatters">
            A list of instances of <see cref="T:MessagePack.Formatters.IMessagePackFormatter`1"/>.
            The formatters are searched in the order given, so if two formatters support serializing the same type, the first one is used.
            </param>
        </member>
        <member name="M:MessagePack.Resolvers.StaticCompositeResolver.Register(MessagePack.IFormatterResolver[])">
            <summary>
            Initializes a singleton instance with the specified formatters and sub-resolvers.
            This method can only call before use StaticCompositeResolver.Instance.GetFormatter.
            If call twice in the Register methods, registered formatters and resolvers will be overridden.
            </summary>
            <param name="resolvers">
            A list of resolvers to use for serializing types.
            The resolvers are searched in the order given, so if two resolvers support serializing the same type, the first one is used.
            </param>
        </member>
        <member name="M:MessagePack.Resolvers.StaticCompositeResolver.Register(System.Collections.Generic.IReadOnlyList{MessagePack.Formatters.IMessagePackFormatter},System.Collections.Generic.IReadOnlyList{MessagePack.IFormatterResolver})">
            <summary>
            Initializes a singleton instance with the specified formatters and sub-resolvers.
            This method can only call before use StaticCompositeResolver.Instance.GetFormatter.
            If call twice in the Register methods, registered formatters and resolvers will be overridden.
            </summary>
            <param name="formatters">
            A list of instances of <see cref="T:MessagePack.Formatters.IMessagePackFormatter`1"/>.
            The formatters are searched in the order given, so if two formatters support serializing the same type, the first one is used.
            </param>
            <param name="resolvers">
            A list of resolvers to use for serializing types for which <paramref name="formatters"/> does not include a formatter.
            The resolvers are searched in the order given, so if two resolvers support serializing the same type, the first one is used.
            </param>
        </member>
        <member name="M:MessagePack.Resolvers.StaticCompositeResolver.GetFormatter``1">
            <summary>
            Gets an <see cref="T:MessagePack.Formatters.IMessagePackFormatter`1"/> instance that can serialize or deserialize some type <typeparamref name="T"/>.
            </summary>
            <typeparam name="T">The type of value to be serialized or deserialized.</typeparam>
            <returns>A formatter, if this resolver supplies one for type <typeparamref name="T"/>; otherwise <see langword="null"/>.</returns>
        </member>
        <member name="T:MessagePack.Resolvers.TypelessContractlessStandardResolver">
            <summary>
            Embeds the full name of .NET types for <see cref="T:System.Object"/> typed fields/collection items
            Preserves .NET <see cref="T:System.DateTime"/> timezone.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.TypelessContractlessStandardResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.TypelessContractlessStandardResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.TypelessContractlessStandardResolver.Resolvers">
            <summary>
            A *private* list of resolvers. If we ever want to expose any of these (so the user can adjust settings, etc.)
            then we must make this an instance collection instead of a static collection so that each consumer can have their own settings.
            </summary>
        </member>
        <member name="T:MessagePack.Resolvers.TypelessObjectResolver">
            <summary>
            Used for `object` fields/collections, ex: var arr = new object[] { 1, "a", new Model() };
            The runtime type of value in object field, should be covered by one of resolvers in complex/standard resolver.
            <see cref="T:MessagePack.Resolvers.TypelessObjectResolver"/> should be placed before DynamicObjectTypeFallbackResolver and <see cref="T:MessagePack.Formatters.PrimitiveObjectFormatter"/> in resolvers list.
            Deserializer uses Namespace.TypeName, AssemblyName to get runtime type in destination app, so that combination must be present in destination app.
            Serialized binary is valid MessagePack binary used ext-format and custom typecode(100).
            Inside ext - assembly qualified type name, and serialized object.
            </summary>
        </member>
        <member name="M:MessagePack.Resolvers.TypelessObjectResolver.GetFormatter``1">
            <inheritdoc />
        </member>
        <member name="F:MessagePack.Resolvers.ForceSizePrimitiveObjectResolver.Instance">
            <summary>
            The singleton instance that can be used.
            </summary>
        </member>
        <member name="F:MessagePack.Resolvers.ForceSizePrimitiveObjectResolver.Options">
            <summary>
            A <see cref="T:MessagePack.MessagePackSerializerOptions"/> instance with this formatter pre-configured.
            </summary>
        </member>
        <member name="T:MessagePack.SkipClrVisibilityChecks">
            <summary>
            Gives a dynamic assembly the ability to skip CLR visibility checks,
            allowing the assembly to access private members of another assembly.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.AttributeBaseClassCtor">
            <summary>
            The <see cref="M:System.Attribute.#ctor"/> constructor.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.AttributeUsageCtor">
            <summary>
            The <see cref="M:System.AttributeUsageAttribute.#ctor(System.AttributeTargets)"/> constructor.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.AttributeUsageAllowMultipleProperty">
            <summary>
            The <see cref="P:System.AttributeUsageAttribute.AllowMultiple"/> property.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.assemblyBuilder">
            <summary>
            The assembly builder that is constructing the dynamic assembly.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.moduleBuilder">
            <summary>
            The module builder for the default module of the <see cref="F:MessagePack.SkipClrVisibilityChecks.assemblyBuilder"/>.
            This is where the special attribute will be defined.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.attributedAssemblyNames">
            <summary>
            The set of assemblies that already have visibility checks skipped for.
            </summary>
        </member>
        <member name="F:MessagePack.SkipClrVisibilityChecks.magicAttributeCtor">
            <summary>
            The constructor on the special attribute to reference for each skipped assembly.
            </summary>
        </member>
        <member name="M:MessagePack.SkipClrVisibilityChecks.#ctor(System.Reflection.Emit.AssemblyBuilder,System.Reflection.Emit.ModuleBuilder)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SkipClrVisibilityChecks"/> class.
            </summary>
            <param name="assemblyBuilder">The builder for the dynamic assembly.</param>
            <param name="moduleBuilder">The builder for the default module defined by <see cref="F:MessagePack.SkipClrVisibilityChecks.assemblyBuilder"/>.</param>
        </member>
        <member name="M:MessagePack.SkipClrVisibilityChecks.GetSkipVisibilityChecksRequirements(System.Reflection.TypeInfo,System.Collections.Immutable.ImmutableHashSet{System.Reflection.AssemblyName}.Builder)">
            <summary>
            Scans a given type for references to non-public types and adds any assemblies that declare those types
            to a given set.
            </summary>
            <param name="typeInfo">The type which may be internal.</param>
            <param name="referencedAssemblies">The set of assemblies to add to where non-public types are found.</param>
        </member>
        <member name="M:MessagePack.SkipClrVisibilityChecks.SkipVisibilityChecksFor(System.Collections.Generic.IEnumerable{System.Reflection.AssemblyName})">
            <summary>
            Add attributes to a dynamic assembly so that the CLR will skip visibility checks
            for the assemblies with the specified names.
            </summary>
            <param name="assemblyNames">The names of the assemblies to skip visibility checks for.</param>
        </member>
        <member name="M:MessagePack.SkipClrVisibilityChecks.SkipVisibilityChecksFor(System.Reflection.AssemblyName)">
            <summary>
            Add an attribute to a dynamic assembly so that the CLR will skip visibility checks
            for the assembly with the specified name.
            </summary>
            <param name="assemblyName">The name of the assembly to skip visibility checks for.</param>
        </member>
        <member name="M:MessagePack.SkipClrVisibilityChecks.GetMagicAttributeCtor">
            <summary>
            Gets the constructor to the IgnoresAccessChecksToAttribute, generating the attribute if necessary.
            </summary>
            <returns>The constructor to the IgnoresAccessChecksToAttribute.</returns>
        </member>
        <member name="M:MessagePack.SkipClrVisibilityChecks.EmitMagicAttribute">
            <summary>
            Defines the special IgnoresAccessChecksToAttribute type in the <see cref="F:MessagePack.SkipClrVisibilityChecks.moduleBuilder"/>.
            </summary>
            <returns>The generated attribute type.</returns>
        </member>
        <member name="T:MessagePack.SequencePool">
            <summary>
            A thread-safe, alloc-free reusable object pool.
            </summary>
        </member>
        <member name="F:MessagePack.SequencePool.Shared">
            <summary>
            A thread-safe pool of reusable <see cref="T:Nerdbank.Streams.Sequence`1"/> objects.
            </summary>
        </member>
        <member name="F:MessagePack.SequencePool.MinimumSpanLength">
            <summary>
            The value to use for <see cref="P:Nerdbank.Streams.Sequence`1.MinimumSpanLength"/>.
            </summary>
            <remarks>
            Individual users that want a different value for this can modify the setting on the rented <see cref="T:Nerdbank.Streams.Sequence`1"/>
            or by supplying their own <see cref="T:System.Buffers.IBufferWriter`1" />.
            </remarks>
            <devremarks>
            We use 32KB so that when LZ4Codec.MaximumOutputLength is used on this length it does not require a
            buffer that would require the Large Object Heap.
            </devremarks>
        </member>
        <member name="F:MessagePack.SequencePool.arrayPoolOrMemoryPool">
            <summary>
            The array pool which we share with all <see cref="T:Nerdbank.Streams.Sequence`1"/> objects created by this <see cref="T:MessagePack.SequencePool"/> instance.
            </summary>
        </member>
        <member name="M:MessagePack.SequencePool.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SequencePool"/> class.
            </summary>
            <remarks>
            We use a <see cref="F:MessagePack.SequencePool.maxSize"/> that allows every processor to be involved in messagepack serialization concurrently,
            plus one nested serialization per processor (since LZ4 and sometimes other nested serializations may exist).
            </remarks>
        </member>
        <member name="M:MessagePack.SequencePool.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SequencePool"/> class.
            </summary>
            <param name="maxSize">The maximum size to allow the pool to grow.</param>
            <devremarks>
            We allow 100 arrays to be shared (instead of the default 50) and reduce the max array length from the default 1MB to something more reasonable for our expected use.
            </devremarks>
        </member>
        <member name="M:MessagePack.SequencePool.#ctor(System.Int32,System.Buffers.ArrayPool{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SequencePool"/> class.
            </summary>
            <param name="maxSize">The maximum size to allow the pool to grow.</param>
            <param name="arrayPool">Array pool that will be used.</param>
        </member>
        <member name="M:MessagePack.SequencePool.#ctor(System.Int32,System.Buffers.MemoryPool{System.Byte})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SequencePool"/> class.
            </summary>
            <param name="maxSize">The maximum size to allow the pool to grow.</param>
            <param name="memoryPool">Memory pool that will be used.</param>
        </member>
        <member name="M:MessagePack.SequencePool.Clear">
            <summary>Clears the pool, letting the <see cref="T:System.GC"/> collect the recycled buffers.</summary>
        </member>
        <member name="M:MessagePack.SequencePool.Rent">
            <summary>
            Gets an instance of <see cref="T:Nerdbank.Streams.Sequence`1"/>
            This is taken from the recycled pool if one is available; otherwise a new one is created.
            </summary>
            <returns>The rental tracker that provides access to the object as well as a means to return it.</returns>
        </member>
        <member name="P:MessagePack.SequencePool.Rental.Value">
            <summary>
            Gets the recyclable object.
            </summary>
        </member>
        <member name="M:MessagePack.SequencePool.Rental.Dispose">
            <summary>
            Returns the recyclable object to the pool.
            </summary>
            <remarks>
            The instance is cleaned first, if a clean delegate was provided.
            It is dropped instead of being returned to the pool if the pool is already at its maximum size.
            </remarks>
        </member>
        <member name="F:MessagePack.SequenceReader`1.usingSequence">
            <summary>
            A value indicating whether we're using <see cref="F:MessagePack.SequenceReader`1.sequence"/> (as opposed to <see cref="F:MessagePack.SequenceReader`1.memory"/>.
            </summary>
        </member>
        <member name="F:MessagePack.SequenceReader`1.sequence">
            <summary>
            Backing for the entire sequence when we're not using <see cref="F:MessagePack.SequenceReader`1.memory"/>.
            </summary>
        </member>
        <member name="F:MessagePack.SequenceReader`1.currentPosition">
            <summary>
            The position at the start of the <see cref="P:MessagePack.SequenceReader`1.CurrentSpan"/>.
            </summary>
        </member>
        <member name="F:MessagePack.SequenceReader`1.nextPosition">
            <summary>
            The position at the end of the <see cref="P:MessagePack.SequenceReader`1.CurrentSpan"/>.
            </summary>
        </member>
        <member name="F:MessagePack.SequenceReader`1.memory">
            <summary>
            Backing for the entire sequence when we're not using <see cref="F:MessagePack.SequenceReader`1.sequence"/>.
            </summary>
        </member>
        <member name="F:MessagePack.SequenceReader`1.moreData">
            <summary>
            A value indicating whether there is unread data remaining.
            </summary>
        </member>
        <member name="F:MessagePack.SequenceReader`1.length">
            <summary>
            The total number of elements in the sequence.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.#ctor(System.Buffers.ReadOnlySequence{`0}@)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SequenceReader`1"/> struct
            over the given <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.#ctor(System.ReadOnlyMemory{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.SequenceReader`1"/> struct
            over the given <see cref="T:System.ReadOnlyMemory`1"/>.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.End">
            <summary>
            Gets a value indicating whether there is no more data in the <see cref="P:MessagePack.SequenceReader`1.Sequence"/>.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.Sequence">
            <summary>
            Gets the underlying <see cref="T:System.Buffers.ReadOnlySequence`1"/> for the reader.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.Position">
            <summary>
            Gets the current position in the <see cref="P:MessagePack.SequenceReader`1.Sequence"/>.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.CurrentSpan">
            <summary>
            Gets the current segment in the <see cref="P:MessagePack.SequenceReader`1.Sequence"/> as a span.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.CurrentSpanIndex">
            <summary>
            Gets the index in the <see cref="P:MessagePack.SequenceReader`1.CurrentSpan"/>.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.UnreadSpan">
            <summary>
            Gets the unread portion of the <see cref="P:MessagePack.SequenceReader`1.CurrentSpan"/>.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.Consumed">
            <summary>
            Gets the total number of <typeparamref name="T"/>'s processed by the reader.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.Remaining">
            <summary>
            Gets remaining <typeparamref name="T"/>'s in the reader's <see cref="P:MessagePack.SequenceReader`1.Sequence"/>.
            </summary>
        </member>
        <member name="P:MessagePack.SequenceReader`1.Length">
            <summary>
            Gets count of <typeparamref name="T"/> in the reader's <see cref="P:MessagePack.SequenceReader`1.Sequence"/>.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.TryPeek(`0@)">
            <summary>
            Peeks at the next value without advancing the reader.
            </summary>
            <param name="value">The next value or default if at the end.</param>
            <returns>False if at the end of the reader.</returns>
        </member>
        <member name="M:MessagePack.SequenceReader`1.TryRead(`0@)">
            <summary>
            Read the next value and advance the reader.
            </summary>
            <param name="value">The next value or default if at the end.</param>
            <returns>False if at the end of the reader.</returns>
        </member>
        <member name="M:MessagePack.SequenceReader`1.Rewind(System.Int64)">
            <summary>
            Move the reader back the specified number of items.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.GetNextSpan">
            <summary>
            Get the next segment with available data, if any.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.Advance(System.Int64)">
            <summary>
            Move the reader ahead the specified number of items.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.AdvanceCurrentSpan(System.Int64)">
            <summary>
            Unchecked helper to avoid unnecessary checks where you know count is valid.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.AdvanceWithinSpan(System.Int64)">
            <summary>
            Only call this helper if you know that you are advancing in the current span
            with valid count and there is no need to fetch the next one.
            </summary>
        </member>
        <member name="M:MessagePack.SequenceReader`1.TryAdvance(System.Int64)">
            <summary>
            Move the reader ahead the specified number of items
            if there are enough elements remaining in the sequence.
            </summary>
            <returns><see langword="true"/> if there were enough elements to advance; otherwise <see langword="false"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReader`1.TryCopyTo(System.Span{`0})">
            <summary>
            Copies data from the current <see cref="P:MessagePack.SequenceReader`1.Position"/> to the given <paramref name="destination"/> span.
            </summary>
            <param name="destination">Destination to copy to.</param>
            <returns>True if there is enough data to copy to the <paramref name="destination"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryRead``1(MessagePack.SequenceReader{System.Byte}@,``0@)">
            <summary>
            Try to read the given type out of the buffer if possible. Warning: this is dangerous to use with arbitrary
            structs- see remarks for full details.
            </summary>
            <remarks>
            IMPORTANT: The read is a straight copy of bits. If a struct depends on specific state of its members to
            behave correctly this can lead to exceptions, etc. If reading endian specific integers, use the explicit
            overloads such as <see cref="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.Int16@)"/>.
            </remarks>
            <returns>
            True if successful. <paramref name="value"/> will be default if failed (due to lack of space).
            </returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryRead(MessagePack.SequenceReader{System.Byte}@,System.SByte@)">
            <summary>
            Reads an <see cref="T:System.SByte"/> from the next position in the sequence.
            </summary>
            <param name="reader">The reader to read from.</param>
            <param name="value">Receives the value read.</param>
            <returns><see langword="true"/> if there was another byte in the sequence; <see langword="false"/> otherwise.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.Int16@)">
            <summary>
            Reads an <see cref="T:System.Int16"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.Int16"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.UInt16@)">
            <summary>
            Reads an <see cref="T:System.UInt16"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.UInt16"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.Int32@)">
            <summary>
            Reads an <see cref="T:System.Int32"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.Int32"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.UInt32@)">
            <summary>
            Reads an <see cref="T:System.UInt32"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.UInt32"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.Int64@)">
            <summary>
            Reads an <see cref="T:System.Int64"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.Int64"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.UInt64@)">
            <summary>
            Reads an <see cref="T:System.UInt64"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for an <see cref="T:System.UInt64"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.Single@)">
            <summary>
            Reads a <see cref="T:System.Single"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for a <see cref="T:System.Single"/>.</returns>
        </member>
        <member name="M:MessagePack.SequenceReaderExtensions.TryReadBigEndian(MessagePack.SequenceReader{System.Byte}@,System.Double@)">
            <summary>
            Reads a <see cref="T:System.Double"/> as big endian.
            </summary>
            <returns>False if there wasn't enough data for a <see cref="T:System.Double"/>.</returns>
        </member>
        <member name="T:MessagePack.SipHash">
            <summary>
            Implements the <see href="https://en.wikipedia.org/wiki/SipHash">SipHash pseudo-random function</see>.
            </summary>
            <remarks>
            This class is immutable and thread-safe.
            </remarks>
        </member>
        <member name="F:MessagePack.SipHash.initialState0">
            <summary>
            Part of the initial 256-bit internal state.
            </summary>
        </member>
        <member name="F:MessagePack.SipHash.initialState1">
            <summary>
            Part of the initial 256-bit internal state.
            </summary>
        </member>
        <member name="M:MessagePack.SipHash.#ctor">
            <summary>Initializes a new instance of the <see cref="T:MessagePack.SipHash"/> class using a random key.</summary>
        </member>
        <member name="M:MessagePack.SipHash.#ctor(System.ReadOnlySpan{System.Byte})">
            <summary>Initializes a new instance of the <see cref="T:MessagePack.SipHash"/> class using the specified 128-bit key.</summary>
            <param name="key">Key for the SipHash pseudo-random function. Must be exactly 16 bytes long.</param>
            <exception cref="T:System.ArgumentException">Thrown when <paramref name="key"/> is not exactly 16 bytes long (128 bits).</exception>
        </member>
        <member name="M:MessagePack.SipHash.GetKey(System.Span{System.Byte})">
            <summary>
            Gets a 128-bit SipHash key.
            </summary>
            <param name="key">The 16-byte buffer that receives the key originally provided to the constructor.</param>
        </member>
        <member name="M:MessagePack.SipHash.Compute(System.ReadOnlySpan{System.Byte})">
            <summary>Computes 64-bit SipHash tag for the specified message.</summary>
            <param name="data">The byte array for which to computer SipHash tag.</param>
            <returns>Returns 64-bit (8 bytes) SipHash tag.</returns>
        </member>
        <member name="T:MessagePack.Utilities">
            <summary>
            Internal utilities and extension methods for various external types.
            </summary>
        </member>
        <member name="M:MessagePack.Utilities.GetEntryEnumerator(System.Collections.IDictionary)">
            <summary>
            Gets an <see cref="T:System.Collections.IDictionary"/> enumerator that does not allocate for each entry,
            and that doesn't produce the nullable ref annotation warning about unboxing a possibly null value.
            </summary>
        </member>
        <member name="T:Nerdbank.Streams.Sequence`1">
            <summary>
            Manages a sequence of elements, readily castable as a <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
            <typeparam name="T">The type of element stored by the sequence.</typeparam>
            <remarks>
            Instance members are not thread-safe.
            </remarks>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Nerdbank.Streams.Sequence`1"/> class
            that uses a private <see cref="T:System.Buffers.ArrayPool`1"/> for recycling arrays.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.#ctor(System.Buffers.MemoryPool{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Nerdbank.Streams.Sequence`1"/> class.
            </summary>
            <param name="memoryPool">The pool to use for recycling backing arrays.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.#ctor(System.Buffers.ArrayPool{`0})">
            <summary>
            Initializes a new instance of the <see cref="T:Nerdbank.Streams.Sequence`1"/> class.
            </summary>
            <param name="arrayPool">The pool to use for recycling backing arrays.</param>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.MinimumSpanLength">
            <summary>
            Gets or sets the minimum length for any array allocated as a segment in the sequence.
            Any non-positive value allows the pool to determine the length of the array.
            </summary>
            <value>The default value is 0.</value>
            <remarks>
            <para>
            Each time <see cref="M:Nerdbank.Streams.Sequence`1.GetSpan(System.Int32)"/> or <see cref="M:Nerdbank.Streams.Sequence`1.GetMemory(System.Int32)"/> is called,
            previously allocated memory is used if it is large enough to satisfy the length demand.
            If new memory must be allocated, the argument to one of these methods typically dictate
            the length of array to allocate. When the caller uses very small values (just enough for its immediate need)
            but the high level scenario can predict that a large amount of memory will be ultimately required,
            it can be advisable to set this property to a value such that just a few larger arrays are allocated
            instead of many small ones.
            </para>
            <para>
            The <see cref="T:System.Buffers.MemoryPool`1"/> in use may itself have a minimum array length as well,
            in which case the higher of the two minimums dictate the minimum array size that will be allocated.
            </para>
            <para>
            If <see cref="P:Nerdbank.Streams.Sequence`1.AutoIncreaseMinimumSpanLength"/> is <see langword="true"/>, this value may be automatically increased as the length of a sequence grows.
            </para>
            </remarks>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.AutoIncreaseMinimumSpanLength">
            <summary>
            Gets or sets a value indicating whether the <see cref="P:Nerdbank.Streams.Sequence`1.MinimumSpanLength"/> should be
            intelligently increased as the length of the sequence grows.
            </summary>
            <remarks>
            This can help prevent long sequences made up of many very small arrays.
            </remarks>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.AsReadOnlySequence">
            <summary>
            Gets this sequence expressed as a <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
            <returns>A read only sequence representing the data in this object.</returns>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.Length">
            <summary>
            Gets the length of the sequence.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.DebuggerDisplay">
            <summary>
            Gets the value to display in a debugger datatip.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.op_Implicit(Nerdbank.Streams.Sequence{`0})~System.Buffers.ReadOnlySequence{`0}">
            <summary>
            Expresses this sequence as a <see cref="T:System.Buffers.ReadOnlySequence`1"/>.
            </summary>
            <param name="sequence">The sequence to convert.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.AdvanceTo(System.SequencePosition)">
            <summary>
            Removes all elements from the sequence from its beginning to the specified position,
            considering that data to have been fully processed.
            </summary>
            <param name="position">
            The position of the first element that has not yet been processed.
            This is typically <see cref="P:System.Buffers.ReadOnlySequence`1.End"/> after reading all elements from that instance.
            </param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.Advance(System.Int32)">
            <summary>
            Advances the sequence to include the specified number of elements initialized into memory
            returned by a prior call to <see cref="M:Nerdbank.Streams.Sequence`1.GetMemory(System.Int32)"/>.
            </summary>
            <param name="count">The number of elements written into memory.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.GetMemory(System.Int32)">
            <summary>
            Gets writable memory that can be initialized and added to the sequence via a subsequent call to <see cref="M:Nerdbank.Streams.Sequence`1.Advance(System.Int32)"/>.
            </summary>
            <param name="sizeHint">The size of the memory required, or 0 to just get a convenient (non-empty) buffer.</param>
            <returns>The requested memory.</returns>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.GetSpan(System.Int32)">
            <summary>
            Gets writable memory that can be initialized and added to the sequence via a subsequent call to <see cref="M:Nerdbank.Streams.Sequence`1.Advance(System.Int32)"/>.
            </summary>
            <param name="sizeHint">The size of the memory required, or 0 to just get a convenient (non-empty) buffer.</param>
            <returns>The requested memory.</returns>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.Append(System.ReadOnlyMemory{`0})">
            <summary>
            Adds an existing memory location to this sequence without copying.
            </summary>
            <param name="memory">The memory to add.</param>
            <remarks>
            This *may* leave significant slack space in a previously allocated block if calls to <see cref="M:Nerdbank.Streams.Sequence`1.Append(System.ReadOnlyMemory{`0})"/>
            follow calls to <see cref="M:Nerdbank.Streams.Sequence`1.GetMemory(System.Int32)"/> or <see cref="M:Nerdbank.Streams.Sequence`1.GetSpan(System.Int32)"/>.
            </remarks>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.Dispose">
            <summary>
            Clears the entire sequence, recycles associated memory into pools,
            and resets this instance for reuse.
            This invalidates any <see cref="T:System.Buffers.ReadOnlySequence`1"/> previously produced by this instance.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.Reset">
            <summary>
            Clears the entire sequence and recycles associated memory into pools.
            This invalidates any <see cref="T:System.Buffers.ReadOnlySequence`1"/> previously produced by this instance.
            </summary>
        </member>
        <member name="F:Nerdbank.Streams.Sequence`1.SequenceSegment.MayContainReferences">
            <summary>
            A value indicating whether the element may contain references (and thus must be cleared).
            </summary>
        </member>
        <member name="F:Nerdbank.Streams.Sequence`1.SequenceSegment.array">
            <summary>
            Gets the backing array, when using an <see cref="T:System.Buffers.ArrayPool`1"/> instead of a <see cref="T:System.Buffers.MemoryPool`1"/>.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.Start">
            <summary>
            Gets the position within <see cref="P:System.Buffers.ReadOnlySequenceSegment`1.Memory"/> where the data starts.
            </summary>
            <remarks>This may be nonzero as a result of calling <see cref="M:Nerdbank.Streams.Sequence`1.AdvanceTo(System.SequencePosition)"/>.</remarks>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.End">
            <summary>
            Gets the position within <see cref="P:System.Buffers.ReadOnlySequenceSegment`1.Memory"/> where the data ends.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.RemainingMemory">
            <summary>
            Gets the tail of memory that has not yet been committed.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.RemainingSpan">
            <summary>
            Gets the tail of memory that has not yet been committed.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.MemoryOwner">
            <summary>
            Gets the tracker for the underlying array for this segment, which can be used to recycle the array when we're disposed of.
            Will be <see langword="null"/> if using an array pool, in which case the memory is held by <see cref="F:Nerdbank.Streams.Sequence`1.SequenceSegment.array"/>.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.AvailableMemory">
            <summary>
            Gets the full memory owned by the <see cref="P:Nerdbank.Streams.Sequence`1.SequenceSegment.MemoryOwner"/>.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.Length">
            <summary>
            Gets the number of elements that are committed in this segment.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.WritableBytes">
            <summary>
            Gets the amount of writable bytes in this segment.
            It is the amount of bytes between <see cref="P:Nerdbank.Streams.Sequence`1.SequenceSegment.Length"/> and <see cref="P:Nerdbank.Streams.Sequence`1.SequenceSegment.End"/>.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.Next">
            <summary>
            Gets or sets the next segment in the singly linked list of segments.
            </summary>
        </member>
        <member name="P:Nerdbank.Streams.Sequence`1.SequenceSegment.IsForeignMemory">
            <summary>
            Gets a value indicating whether this segment refers to memory that came from outside and that we cannot write to nor recycle.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.Assign(System.Buffers.IMemoryOwner{`0})">
            <summary>
            Assigns this (recyclable) segment a new area in memory.
            </summary>
            <param name="memoryOwner">The memory and a means to recycle it.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.Assign(`0[])">
            <summary>
            Assigns this (recyclable) segment a new area in memory.
            </summary>
            <param name="array">An array drawn from an <see cref="T:System.Buffers.ArrayPool`1"/>.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.AssignForeign(System.ReadOnlyMemory{`0})">
            <summary>
            Assigns this (recyclable) segment a new area in memory.
            </summary>
            <param name="memory">A memory block obtained from outside, that we do not own and should not recycle.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.ResetMemory(System.Buffers.ArrayPool{`0})">
            <summary>
            Clears all fields in preparation to recycle this instance.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.SetNext(Nerdbank.Streams.Sequence{`0}.SequenceSegment)">
            <summary>
            Adds a new segment after this one.
            </summary>
            <param name="segment">The next segment in the linked list.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.Advance(System.Int32)">
            <summary>
            Commits more elements as written in this segment.
            </summary>
            <param name="count">The number of elements written.</param>
        </member>
        <member name="M:Nerdbank.Streams.Sequence`1.SequenceSegment.AdvanceTo(System.Int32)">
            <summary>
            Removes some elements from the start of this segment.
            </summary>
            <param name="offset">The number of elements to ignore from the start of the underlying array.</param>
        </member>
        <member name="M:Nerdbank.Streams.Requires.Range(System.Boolean,System.String,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> if a condition does not evaluate to true.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Requires.FailRange(System.String,System.String)">
            <summary>
            Throws an <see cref="T:System.ArgumentOutOfRangeException"/> if a condition does not evaluate to true.
            </summary>
            <returns>Nothing.  This method always throws.</returns>
        </member>
        <member name="M:Nerdbank.Streams.Requires.NotNull``1(``0,System.String)">
            <summary>
            Throws an exception if the specified parameter's value is null.
            </summary>
            <typeparam name="T">The type of the parameter.</typeparam>
            <param name="value">The value of the argument.</param>
            <param name="parameterName">The name of the parameter to include in any thrown exception.</param>
            <returns>The value of the parameter.</returns>
            <exception cref="T:System.ArgumentNullException">Thrown if <paramref name="value"/> is <see langword="null"/>.</exception>
        </member>
        <member name="M:Nerdbank.Streams.Requires.Argument(System.Boolean,System.String,System.String)">
            <summary>
            Throws an ArgumentException if a condition does not evaluate to true.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Requires.Argument(System.Boolean,System.String,System.String,System.Object)">
            <summary>
            Throws an ArgumentException if a condition does not evaluate to true.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Requires.Argument(System.Boolean,System.String,System.String,System.Object,System.Object)">
            <summary>
            Throws an ArgumentException if a condition does not evaluate to true.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Requires.Argument(System.Boolean,System.String,System.String,System.Object[])">
            <summary>
            Throws an ArgumentException if a condition does not evaluate to true.
            </summary>
        </member>
        <member name="T:Nerdbank.Streams.Verify">
            <summary>
            Common runtime checks that throw exceptions upon failure.
            </summary>
        </member>
        <member name="M:Nerdbank.Streams.Verify.Operation(System.Boolean,System.String)">
            <summary>
            Throws an <see cref="T:System.InvalidOperationException"/> if a condition is false.
            </summary>
        </member>
    </members>
</doc>
