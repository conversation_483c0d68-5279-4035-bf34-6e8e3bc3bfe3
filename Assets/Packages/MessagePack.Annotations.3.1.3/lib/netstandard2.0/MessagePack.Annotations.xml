<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MessagePack.Annotations</name>
    </assembly>
    <members>
        <member name="T:MessagePack.MessagePackKnownFormatterAttribute">
            <summary>
            Identifies a custom formatter (one that implements one or more <c>IMessagePackFormatter&lt;T&gt;</c> interfaces)
            that should be considered when checking that types are serializable and/or included in a source-generated resolver.
            </summary>
            <remarks>
            Formatters identified with this attribute will be included in the source-generated resolver.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackKnownFormatterAttribute.FormatterType">
            <summary>
            Gets a type that implements one or more <c>IMessagePackFormatter&lt;T&gt;</c> interfaces.
            </summary>
        </member>
        <member name="T:MessagePack.MessagePackAssumedFormattableAttribute">
            <summary>
            Identifies a type for which a <c>IMessagePackFormatter&lt;T&gt;</c> exists and will be added manually to the <c>IFormatterResolver</c> by the program.
            </summary>
            <remarks>
            <para>This attribute suppresses warnings by the MessagePack analyzer when it encounters references to the specified type within another serializable type.</para>
            <para>When possible, using the <see cref="T:MessagePack.MessagePackKnownFormatterAttribute"/> is preferred.</para>
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackAssumedFormattableAttribute.FormattableType">
            <summary>
            Gets a type for which an <c>IMessagePackFormatter&lt;T&gt;</c> is known to exist and will be added via a custom resolver by the program at runtime.
            </summary>
        </member>
        <member name="T:MessagePack.ExcludeFormatterFromSourceGeneratedResolverAttribute">
            <summary>
            Causes the source generated resolver, which typically includes all implementations of <c>IMessagePackFormatter&lt;T&gt;</c>,
            to exclude this particular formatter.
            </summary>
            <remarks>
            This is useful when the formatter is intended for special case members,
            which may apply the <see cref="T:MessagePack.MessagePackFormatterAttribute"/> to select the private formatter.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackObjectAttribute.KeyAsPropertyName">
            <summary>
            Gets a value indicating whether to automatically serialize all internal and public fields and properties using their property name as the key in a map.
            </summary>
        </member>
        <member name="M:MessagePack.MessagePackObjectAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.MessagePackObjectAttribute"/> class.
            </summary>
            <param name="keyAsPropertyName">
            <see langword="true" /> to automatically serialize all internal and public fields and properties using their property name as the key in a map;
            or <see langword="false" /> to use the <see cref="T:MessagePack.KeyAttribute"/> attribute to specify the key for each field or property.
            </param>
        </member>
        <member name="P:MessagePack.MessagePackObjectAttribute.SuppressSourceGeneration">
            <summary>
            Gets or sets a value indicating whether the source generator should <em>not</em>
            generate a formatter for this type at compile-time.
            </summary>
            <remarks>
            By default, source generators will generate a formatter for every type that is annotated with
            this attribute to improve startup performance.
            However if this leads to malfunctions during code generation or at runtime,
            it can be disabled by setting this property to <see langword="true"/>.
            When no precompiled formatter is found at runtime, the <c>DynamicObjectResolver</c>
            will generate a formatter at runtime instead.
            </remarks>
        </member>
        <member name="P:MessagePack.MessagePackObjectAttribute.AllowPrivate">
            <summary>
            Gets or sets a value indicating whether generated formatters should allow non-public members to be serialized and deserialized.
            </summary>
            <remarks>
            <para>
            This is particularly important to set when <see cref="P:MessagePack.MessagePackObjectAttribute.KeyAsPropertyName"/> is set to <see langword="true" /> if non-public members should be included.
            </para>
            <para>
            This property can also ensure non-public members are serialized by the <c>DynamicObjectResolver</c> so that the application doesn't have to use
            <c>DynamicObjectResolverAllowPrivate</c> explicitly for an object to be properly serialized.
            </para>
            </remarks>
        </member>
        <member name="P:MessagePack.UnionAttribute.Key">
            <summary>
            Gets the distinguishing value that identifies a particular subtype.
            </summary>
        </member>
        <member name="P:MessagePack.UnionAttribute.SubType">
            <summary>
            Gets the derived or implementing type.
            </summary>
        </member>
        <member name="M:MessagePack.UnionAttribute.#ctor(System.Int32,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.UnionAttribute"/> class.
            </summary>
            <param name="key">The distinguishing value that identifies a particular subtype.</param>
            <param name="subType">The derived or implementing type.</param>
        </member>
        <member name="M:MessagePack.UnionAttribute.#ctor(System.Int32,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:MessagePack.UnionAttribute"/> class.
            </summary>
            <param name="key">The distinguishing value that identifies a particular subtype.</param>
            <param name="subType">The full name (should be assembly qualified) of the derived or implementing type.</param>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>
            Specifies that null is allowed as an input even if the corresponding type disallows it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute">
            <summary>
            Indicates that the specified method parameter expects a constant.
            </summary>
            <remarks>
            This can be used to inform tooling that a constant should be used as an argument for the annotated parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute.Min">
            <summary>
            Indicates the minimum bound of the expected constant, inclusive.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ConstantExpectedAttribute.Max">
            <summary>
            Indicates the maximum bound of the expected constant, inclusive.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>
            Specifies that null is disallowed as an input even if the corresponding type allows it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>
            Applied to a method that will never return under any circumstance.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>
            Specifies that the method will not return if the associated Boolean parameter is passed the specified value.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes the attribute with the specified parameter value.
            </summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable
            by diagnostics if the argument to the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>
            Gets the condition parameter value.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute">
            <summary>
            Indicates that an API is experimental and it may change in the future.
            </summary>
            <remarks>
            This attribute allows call sites to be flagged with a diagnostic that indicates that an experimental
            feature is used. Authors can use this attribute to ship preview features in their assemblies.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.ExperimentalAttribute"/> class,
            specifying the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <param name="diagnosticId">The ID that the compiler will use when reporting a use of the API the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.DiagnosticId">
            <summary>
            Gets the ID that the compiler will use when reporting a use of the API the attribute applies to.
            </summary>
            <value>The unique diagnostic ID.</value>
            <remarks>
            The diagnostic ID is shown in build output for warnings and errors.
            <para>This property represents the unique ID that can be used to suppress the warnings or errors, if needed.</para>
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.ExperimentalAttribute.UrlFormat">
            <summary>
            Gets or sets the URL for corresponding documentation.
            The API accepts a format string instead of an actual URL, creating a generic URL that includes the diagnostic ID.
            </summary>
            <value>The format string that represents a URL to corresponding documentation.</value>
            <remarks>An example format string is <c>https://contoso.com/obsoletion-warnings/{0}</c>.</remarks>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>
            Specifies that an output may be null even if the corresponding type disallows it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>
            Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes the attribute with the specified return value condition.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter may be null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>
            Gets the return value condition.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property members have not-null values.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>
            Initializes the attribute with a field or property member.
            </summary>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>
            Initializes the attribute with the list of field and property members.
            </summary>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>
            Specifies that the method or property will ensure that the listed field and property
            members have not-null values when returning with the specified return value condition.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>
            Initializes the attribute with the specified return value condition and a field or property member.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="member">The field or property member that is promised to be not-null.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>
            Initializes the attribute with the specified return value condition and list of field and property members.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
            <param name="members">The list of field and property members that are promised to be not-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>
            Gets the return value condition.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>
            Gets field or property member names.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>
            Specifies that an output will not be null even if the corresponding type allows it.
            Specifies that an input argument was not null when the call returns.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>
            Specifies that the output will be non-null if the named parameter is non-null.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>
            Initializes the attribute with the associated parameter name.
            </summary>
            <param name="parameterName">The associated parameter name. The output will be non-null if the argument to the parameter specified is non-null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>
            Gets the associated parameter name.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>
            Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>
            Initializes the attribute with the specified return value condition.
            </summary>
            <param name="returnValue">The return value condition. If the method returns this value, the associated parameter will not be null.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.SetsRequiredMembersAttribute">
            <summary>
            Specifies that this constructor sets all required members for the current type,
            and callers do not need to set any required members themselves.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute">
            <summary>
            Specifies the syntax used in a string.
            </summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String)">
            <summary>
            Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.
            </summary>
            <param name="syntax">The syntax identifier.</param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.#ctor(System.String,System.Object[])">
            <summary>Initializes the <see cref="T:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute"/> with the identifier of the syntax used.</summary>
            <param name="syntax">The syntax identifier.</param>
            <param name="arguments">Optional arguments associated with the specific syntax employed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Syntax">
            <summary>Gets the identifier of the syntax used.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Arguments">
            <summary>Optional arguments associated with the specific syntax employed.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.CompositeFormat">
            <summary>The syntax identifier for strings containing composite formats for string formatting.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateOnlyFormat">
            <summary>The syntax identifier for strings containing date format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.DateTimeFormat">
            <summary>The syntax identifier for strings containing date and time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.EnumFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Enum"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.GuidFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.Guid"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Json">
            <summary>The syntax identifier for strings containing JavaScript Object Notation (JSON).</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.NumericFormat">
            <summary>The syntax identifier for strings containing numeric format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Regex">
            <summary>The syntax identifier for strings containing regular expressions.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeOnlyFormat">
            <summary>The syntax identifier for strings containing time format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.TimeSpanFormat">
            <summary>The syntax identifier for strings containing <see cref="T:System.TimeSpan"/> format specifiers.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Uri">
            <summary>The syntax identifier for strings containing URIs.</summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.StringSyntaxAttribute.Xml">
            <summary>The syntax identifier for strings containing XML.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnscopedRefAttribute">
            <summary>
            Used to indicate a byref escapes and is not scoped.
            </summary>
            <remarks>
            <para>
            There are several cases where the C# compiler treats a <see langword="ref"/> as implicitly
            <see langword="scoped"/> - where the compiler does not allow the <see langword="ref"/> to escape the method.
            </para>
            <para>
            For example:
            <list type="number">
                <item><see langword="this"/> for <see langword="struct"/> instance methods.</item>
                <item><see langword="ref"/> parameters that refer to <see langword="ref"/> <see langword="struct"/> types.</item>
                <item><see langword="out"/> parameters.</item>
            </list>
            </para>
            <para>
            This attribute is used in those instances where the <see langword="ref"/> should be allowed to escape.
            </para>
            <para>
            Applying this attribute, in any form, has impact on consumers of the applicable API. It is necessary for
            API authors to understand the lifetime implications of applying this attribute and how it may impact their users.
            </para>
            </remarks>
        </member>
        <member name="T:System.Index">
            <summary>Represent a type can be used to index a collection either from the start or the end.</summary>
            <remarks>
            Index is used by the C# compiler to support the new index syntax
            <code>
            int[] someArray = new int[5] { 1, 2, 3, 4, 5 } ;
            int lastElement = someArray[^1]; // lastElement = 5
            </code>
            </remarks>
        </member>
        <member name="M:System.Index.#ctor(System.Int32,System.Boolean)">
            <summary>Construct an Index using a value and indicating if the index is from the start or from the end.</summary>
            <param name="value">The index value. it has to be zero or positive number.</param>
            <param name="fromEnd">Indicating if the index is from the start or from the end.</param>
            <remarks>
            If the Index constructed from the end, index value 1 means pointing at the last element and index value 0 means pointing at beyond last element.
            </remarks>
        </member>
        <member name="P:System.Index.Start">
            <summary>Create an Index pointing at first element.</summary>
        </member>
        <member name="P:System.Index.End">
            <summary>Create an Index pointing at beyond last element.</summary>
        </member>
        <member name="M:System.Index.FromStart(System.Int32)">
            <summary>Create an Index from the start at the position indicated by the value.</summary>
            <param name="value">The index value from the start.</param>
        </member>
        <member name="M:System.Index.FromEnd(System.Int32)">
            <summary>Create an Index from the end at the position indicated by the value.</summary>
            <param name="value">The index value from the end.</param>
        </member>
        <member name="P:System.Index.Value">
            <summary>Returns the index value.</summary>
        </member>
        <member name="P:System.Index.IsFromEnd">
            <summary>Indicates whether the index is from the start or the end.</summary>
        </member>
        <member name="M:System.Index.GetOffset(System.Int32)">
            <summary>Calculate the offset from the start using the giving collection length.</summary>
            <param name="length">The length of the collection that the Index will be used with. length has to be a positive value</param>
            <remarks>
            For performance reason, we don't validate the input length parameter and the returned offset value against negative values.
            we don't validate either the returned offset is greater than the input length.
            It is expected Index will be used with collections which always have non negative length/count. If the returned offset is negative and
            then used to index a collection will get out of range exception which will be same affect as the validation.
            </remarks>
        </member>
        <member name="M:System.Index.Equals(System.Object)">
            <summary>Indicates whether the current Index object is equal to another object of the same type.</summary>
            <param name="value">An object to compare with this object</param>
        </member>
        <member name="M:System.Index.Equals(System.Index)">
            <summary>Indicates whether the current Index object is equal to another Index object.</summary>
            <param name="other">An object to compare with this object</param>
        </member>
        <member name="M:System.Index.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
        </member>
        <member name="M:System.Index.op_Implicit(System.Int32)~System.Index">
            <summary>Converts integer number to an Index.</summary>
        </member>
        <member name="M:System.Index.ToString">
            <summary>Converts the value of the current Index object to its equivalent string representation.</summary>
        </member>
        <member name="T:System.Range">
            <summary>Represent a range has start and end indexes.</summary>
            <remarks>
            Range is used by the C# compiler to support the range syntax.
            <code>
            int[] someArray = new int[5] { 1, 2, 3, 4, 5 };
            int[] subArray1 = someArray[0..2]; // { 1, 2 }
            int[] subArray2 = someArray[1..^0]; // { 2, 3, 4, 5 }
            </code>
            </remarks>
        </member>
        <member name="P:System.Range.Start">
            <summary>Represent the inclusive start index of the Range.</summary>
        </member>
        <member name="P:System.Range.End">
            <summary>Represent the exclusive end index of the Range.</summary>
        </member>
        <member name="M:System.Range.#ctor(System.Index,System.Index)">
            <summary>Construct a Range object using the start and end indexes.</summary>
            <param name="start">Represent the inclusive start index of the range.</param>
            <param name="end">Represent the exclusive end index of the range.</param>
        </member>
        <member name="M:System.Range.Equals(System.Object)">
            <summary>Indicates whether the current Range object is equal to another object of the same type.</summary>
            <param name="value">An object to compare with this object</param>
        </member>
        <member name="M:System.Range.Equals(System.Range)">
            <summary>Indicates whether the current Range object is equal to another Range object.</summary>
            <param name="other">An object to compare with this object</param>
        </member>
        <member name="M:System.Range.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
        </member>
        <member name="M:System.Range.ToString">
            <summary>Converts the value of the current Range object to its equivalent string representation.</summary>
        </member>
        <member name="M:System.Range.StartAt(System.Index)">
            <summary>Create a Range object starting from start index to the end of the collection.</summary>
        </member>
        <member name="M:System.Range.EndAt(System.Index)">
            <summary>Create a Range object starting from first element in the collection to the end Index.</summary>
        </member>
        <member name="P:System.Range.All">
            <summary>Create a Range object starting from first element to the end.</summary>
        </member>
        <member name="M:System.Range.GetOffsetAndLength(System.Int32)">
            <summary>Calculate the start offset and length of range object using a collection length.</summary>
            <param name="length">The length of the collection that the range will be used with. length has to be a positive value.</param>
            <remarks>
            For performance reason, we don't validate the input length parameter against negative values.
            It is expected Range will be used with collections which always have non negative length/count.
            We validate the range is inside the length scope though.
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.AsyncMethodBuilderAttribute">
            <summary>
            Indicates the type of the async method builder that should be used by a language compiler to
            build the attributed async method or to build the attributed type when used as the return type
            of an async method.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.AsyncMethodBuilderAttribute.#ctor(System.Type)">
            <summary>Initializes the <see cref="T:System.Runtime.CompilerServices.AsyncMethodBuilderAttribute"/>.</summary>
            <param name="builderType">The <see cref="T:System.Type"/> of the associated builder.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.AsyncMethodBuilderAttribute.BuilderType">
            <summary>Gets the <see cref="T:System.Type"/> of the associated builder.</summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute">
            <summary>
            An attribute that allows parameters to receive the expression of other parameters.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute"/> class.
            </summary>
            <param name="parameterName">The condition parameter value.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CallerArgumentExpressionAttribute.ParameterName">
            <summary>
            Gets the parameter name the expression is retrieved from.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CollectionBuilderAttribute.#ctor(System.Type,System.String)">
            <summary>
            Initialize the attribute to refer to the <paramref name="methodName"/> method on the <paramref name="builderType"/> type.
            </summary>
            <param name="builderType">The type of the builder to use to construct the collection.</param>
            <param name="methodName">The name of the method on the builder to use to construct the collection.</param>
            <remarks>
            <paramref name="methodName"/> must refer to a static method that accepts a single parameter of
            type <see cref="!:ReadOnlySpan&lt;T&gt;"/> and returns an instance of the collection being built containing
            a copy of the data from that span.  In future releases of .NET, additional patterns may be supported.
            </remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.BuilderType">
            <summary>
            Gets the type of the builder to use to construct the collection.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CollectionBuilderAttribute.MethodName">
            <summary>
            Gets the name of the method on the builder to use to construct the collection.
            </summary>
            <remarks>
            This should match the metadata name of the target method.
            For example, this might be ".ctor" if targeting the type's constructor.
            </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute">
            <summary>
            Indicates that compiler support for a particular feature is required for the location where this attribute is applied.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.#ctor(System.String)">
            <summary>
            Creates a new instance of the <see cref="T:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute"/> type.
            </summary>
            <param name="featureName">The name of the feature to indicate.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName">
            <summary>
            The name of the compiler feature.
            </summary>
        </member>
        <member name="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.IsOptional">
            <summary>
            If true, the compiler can choose to allow access to the location where this attribute is applied if it does not understand <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RefStructs">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the ref structs C# feature.
            </summary>
        </member>
        <member name="F:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.RequiredMembers">
            <summary>
            The <see cref="P:System.Runtime.CompilerServices.CompilerFeatureRequiredAttribute.FeatureName"/> used for the required members C# feature.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute">
            <summary>
            Indicates which arguments to a method involving an interpolated string handler should be passed to that handler.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="argument">The name of the argument that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="M:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.#ctor(System.String[])">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute"/> class.
            </summary>
            <param name="arguments">The names of the arguments that should be passed to the handler.</param>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="P:System.Runtime.CompilerServices.InterpolatedStringHandlerArgumentAttribute.Arguments">
            <summary>
            Gets the names of the arguments that should be passed to the handler.
            </summary>
            <remarks><see langword="null"/> may be used as the name of the receiver in an instance method.</remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.InterpolatedStringHandlerAttribute">
            <summary>
            Indicates the attributed type is to be used as an interpolated string handler.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.IsExternalInit">
            <summary>
            Reserved to be used by the compiler for tracking metadata.
            This class should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ModuleInitializerAttribute">
             <summary>
             Used to indicate to the compiler that a method should be called
             in its containing module's initializer.
             </summary>
             <remarks>
             When one or more valid methods
             with this attribute are found in a compilation, the compiler will
             emit a module initializer which calls each of the attributed methods.
            
             Certain requirements are imposed on any method targeted with this attribute:
             - The method must be `static`.
             - The method must be an ordinary member method, as opposed to a property accessor, constructor, local function, etc.
             - The method must be parameterless.
             - The method must return `void`.
             - The method must not be generic or be contained in a generic type.
             - The method's effective accessibility must be `internal` or `public`.
            
             The specification for module initializers in the .NET runtime can be found here:
             https://github.com/dotnet/runtime/blob/main/docs/design/specs/Ecma-335-Augments.md#module-initializer
             </remarks>
        </member>
        <member name="T:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute">
            <summary>
            Specifies the priority of a member in overload resolution. When unspecified, the default priority is 0.
            </summary>
        </member>
        <member name="M:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute"/> class.
            </summary>
            <param name="priority">The priority of the attributed member. Higher numbers are prioritized, lower numbers are deprioritized. 0 is the default if no attribute is present.</param>
        </member>
        <member name="P:System.Runtime.CompilerServices.OverloadResolutionPriorityAttribute.Priority">
            <summary>
            The priority of the member.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.ParamCollectionAttribute">
            <summary>
            Indicates that a method will allow a variable number of arguments in its invocation.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiredMemberAttribute">
            <summary>
            Specifies that a type has required members or that a member is required.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.RequiresLocationAttribute">
            <summary>
            Reserved for use by a compiler for tracking metadata.
            This attribute should not be used by developers in source code.
            </summary>
        </member>
        <member name="T:System.Runtime.CompilerServices.SkipLocalsInitAttribute">
            <summary>
            Used to indicate to the compiler that the <c>.locals init</c> flag should not be set in method headers.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class.
            </summary>
        </member>
        <member name="M:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute"/> class with the specified message.
            </summary>
            <param name="message">An optional message associated with this attribute instance.</param>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Message">
            <summary>
            Returns the optional message associated with this attribute instance.
            </summary>
        </member>
        <member name="P:System.Runtime.Versioning.RequiresPreviewFeaturesAttribute.Url">
            <summary>
            Returns the optional URL associated with this attribute instance.
            </summary>
        </member>
    </members>
</doc>
