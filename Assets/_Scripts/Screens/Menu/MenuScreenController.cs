using _MetaApi;
using _Scripts.Screens.Pause;
using Core.Match.Presenter;
using Core.Scenes;
using Core.ScreenManagement;
using VContainer;

namespace Screens.Menu
{
    [Preserve]
    public class MenuScreenController : ScreenController<MenuScreen>
    {
        private readonly IUserApiContext _userApiContext;
        private readonly INavigator _navigator;
        private readonly IMatchPresenter _matchPresenter;

        [Preserve]
        public MenuScreenController(
            IUserApiContext userApiContext,
            INavigator navigator,
            IMatchPresenter matchPresenter)
        {
            _userApiContext = userApiContext;
            _navigator = navigator;
            _matchPresenter = matchPresenter;
        }

        protected override void OnCreatedInternal()
        {
            base.OnCreatedInternal();
            View.Resources.Init(_userApiContext.Progress);
        }

        protected override void ShowInternal()
        {
            //View.Nickname.Text = _userApiContext.Identification.Nickname;
            //View.Resources.Show();
            
            DisOnHide(View.SettingsButton.AddOnClick(() => _navigator.Show<SettingsScreen>()));
            DisOnHide(View.PlayButton.AddOnClick(OnPlayClicked));
        }

        private void OnPlayClicked()
        {
            _matchPresenter.ShowLocalhostAndBots();
        }

        protected override void HideInternal()
        {
            View.Resources.Hide();
        }
    }
}