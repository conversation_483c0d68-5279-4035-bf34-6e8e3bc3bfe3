using System;
using _Scripts.Ui.Core.ResourcesContainer;
using Core.ScreenManagement;
using Ui.Core.Button;
using Ui.Core.Text;
using UnityEngine;

namespace Screens.Menu
{
    public class MenuScreen : ScreenMono
    {
        public override Type ControllerType => typeof(MenuScreenController);

        [field: SerializeField] 
        public TextMono Nickname { get; private set; }

        [field: SerializeField]
        public ResourcesMono Resources { get; private set; }

        [field: SerializeField]
        public ButtonMono SettingsButton { get; private set; }
        
        [field: SerializeField]
        public ButtonMono PlayButton { get; private set; }
    }
}