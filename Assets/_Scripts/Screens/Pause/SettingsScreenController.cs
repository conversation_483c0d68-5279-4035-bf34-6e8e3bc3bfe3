using Core.ScreenManagement;
using UnityEngine.Scripting;

namespace _Scripts.Screens.Pause
{
    [Preserve]
    public class SettingsScreenController : ScreenController<SettingsScreen>
    {
        [Preserve]
        public SettingsScreenController()
        {
        }
        
        public override ScreenShowMode ShowMode => ScreenShowMode.Additive;

        protected override void ShowInternal()
        {
            DisOnHide(View.BackgroundBackButton.AddOnClick(() => Exit()));
        }

        protected override void HideInternal()
        {
        }
    }
}