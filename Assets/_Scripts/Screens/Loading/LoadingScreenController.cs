using Core.ScreenManagement;
using Core.ScreenManagement.Layers;
using UnityEngine.Scripting;

namespace Screens.Loading
{
    [Preserve]
    public class LoadingScreenController : ScreenController<LoadingScreen, LoadingScreenParameters>
    {
        [Preserve]
        public LoadingScreenController()
        { }

        public override ScreenLayerType LayerType => ScreenLayerType.Loading;

        protected override void ShowInternal()
        {
            DisOnHide(Parameters.ProgressController.Progress01.Listen(OnProgressChange));
            DisOnHide(Parameters.HideRx.Listen(() => Exit()));
        }

        protected override void HideInternal()
        {
        }

        private void OnProgressChange(float progress)
        {
            View.ProgressBar.SetProgress(progress);
        }
    }
}