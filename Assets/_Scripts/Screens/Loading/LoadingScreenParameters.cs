using Core.Rx;

namespace Screens.Loading
{
    public class LoadingScreenParameters
    {
        public LoadingScreenParameters(ILoadingScreenProgressController progressController, IRx hideRx)
        {
            ProgressController = progressController;
            HideRx = hideRx;
        }

        public ILoadingScreenProgressController ProgressController { get; }
        public IRx HideRx { get; }
    }
}