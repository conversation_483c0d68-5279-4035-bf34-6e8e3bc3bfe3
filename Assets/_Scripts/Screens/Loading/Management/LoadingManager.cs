using Core.Rx;
using Core.ScreenManagement;
using UnityEngine.Scripting;

namespace Screens.Loading.Management
{
    [Preserve]
    public class LoadingManager : ILoadingManager
    {
        private readonly INavigator _navigator;
        private LoadingHandler _previousHandler;

        [Preserve]
        public LoadingManager(INavigator navigator)
        {
            _navigator = navigator;
        }
        
        public LoadingHandler Show()
        {
            _previousHandler?.Dispose();
            
            var progressController = new LoadingScreenProgressController();
            var hideRx = new Rx();
            var parameters = new LoadingScreenParameters(progressController, hideRx);
            var handler = new LoadingHandler(progressController, hideRx);
            _previousHandler = handler;
            _navigator.Show<LoadingScreen>(parameters);
            
            return handler;
        }

        public LoadingHandler Continue()
        {
            return _previousHandler ?? Show();
        }
    }
}