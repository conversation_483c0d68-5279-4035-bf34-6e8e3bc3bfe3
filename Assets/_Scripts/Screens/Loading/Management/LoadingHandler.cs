using System;
using Core.Rx;

namespace Screens.Loading.Management
{
    public class LoadingHandler : IDisposable
    {
        private readonly LoadingScreenProgressController _progressController;
        private readonly Rx _hideLoadingRx;

        private bool _disposed;

        public LoadingHandler(LoadingScreenProgressController progressController,
            Rx hideLoadingRx)
        {
            _progressController = progressController;
            _hideLoadingRx = hideLoadingRx;
        }

        public void SetProgress(float progress01)
        {
            _progressController.SetProgress(progress01);
        }

        public void Dispose()
        {
            if (_disposed)
                return;

            _disposed = true;
            _hideLoadingRx.Call();
        }
    }
}