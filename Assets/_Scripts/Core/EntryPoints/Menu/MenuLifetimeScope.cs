using Core.ScreenManagement.Layers;
using VContainer;
using VContainer.Unity;

namespace Core.EntryPoints.Menu
{
    public class MenuLifetimeScope : LifetimeScope
    {
        protected override void Configure(IContainerBuilder builder)
        {
            base.Configure(builder);
            
            builder.Register<IMatchPresenter, MatchPresenter>(Lifetime.Singleton);
            
            builder.RegisterEntryPoint<MenuEntryPoint>();
        }
    }
}