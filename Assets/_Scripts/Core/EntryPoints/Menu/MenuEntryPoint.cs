using Core.Scenes;
using Core.ScreenManagement;
using Cysharp.Threading.Tasks;
using Screens.Menu;
using VContainer.Unity;

namespace Core.EntryPoints.Menu
{
    [UnityEngine.Scripting.Preserve]
    public class MenuEntryPoint : IStartable
    {
        private readonly INavigator _navigator;
        private readonly ISceneNavigator _sceneNavigator;
        
        [UnityEngine.Scripting.Preserve]
        public MenuEntryPoint(
            INavigator navigator,
            ISceneNavigator sceneNavigator)
        {
            _navigator = navigator;
            _sceneNavigator = sceneNavigator;
        }

        public void Start() => StartAsync().Forget();

        private async UniTaskVoid StartAsync()
        {
            await UniTask.WaitUntil(() => _sceneNavigator.Current.Value == SceneType.Menu);
            _navigator.Show<MenuScreen>();
        }
    }
}