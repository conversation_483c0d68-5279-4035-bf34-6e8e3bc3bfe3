using _MetaApi;
using Core.Match.Presenter;
using Core.Scenes;
using Core.ScreenManagement;
using Core.ScreenManagement.Layers;
using Screens.Loading.Management;
using Senders;
using VContainer;
using VContainer.Unity;

namespace Core.EntryPoints.Init
{
    public class GlobalLifetimeScope : LifetimeScope
    {
        protected override void Configure(IContainerBuilder builder)
        {
            base.Configure(builder);

            builder.Register<ISceneNavigator, SceneNavigator>(Lifetime.Singleton);
            builder.Register<IScreenLayersManager, ScreenLayersManager>(Lifetime.Singleton);
            builder.Register<INavigator, Navigator>(Lifetime.Singleton);
            builder.Register<ILoadingManager, LoadingManager>(Lifetime.Singleton);

            builder.Register<IHttpCommunicator, HttpCommunicator>(Lifetime.Singleton);
            builder.Register<IUserApiContext, UserApiContext>(Lifetime.Singleton);
            
            builder.Register<IMatchPresenter, MatchPresenter>(Lifetime.Singleton);
        }
    }
}