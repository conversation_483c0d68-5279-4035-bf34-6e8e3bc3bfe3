using _MetaApi;
using Core.Match.Presenter;
using Core.Scenes;
using Core.ScreenManagement;
using Core.ScreenManagement.Layers;
using Screens.Loading.Management;
using Senders;
using Zenject;

namespace Core.EntryPoints.Init
{
    public class GlobalInstaller : MonoInstaller<GlobalInstaller>
    {
        public override void InstallBindings()
        {
            var scope = Container;

            scope.Bind<ISceneNavigator>().To<SceneNavigator>().AsSingle();
            scope.Bind<IScreenLayersManager>().To<ScreenLayersManager>().AsSingle();
            scope.Bind<INavigator>().To<Navigator>().AsSingle();
            scope.Bind<ILoadingManager>().To<LoadingManager>().AsSingle();

            scope.Bind<IHttpCommunicator>().To<HttpCommunicator>().AsSingle();
            scope.Bind<IUserApiContext>().To<UserApiContext>().AsSingle();

            scope.Bind<IMatchPresenter>().To<MatchPresenter>().As<PERSON>ingle();
        }
    }
}