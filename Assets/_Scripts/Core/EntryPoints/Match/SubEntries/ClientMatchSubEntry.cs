using System;
using Core.Disposable;
using Core.ScreenManagement;
using Screens.Match;
using UnityEngine.Scripting;

#if !DEDICATED_SERVER
namespace Core.EntryPoints.Match.SubEntries
{
    [Preserve]
    public class ClientMatchSubEntry : MatchSubEntry
    {
        private readonly INavigator _navigator;

        [Preserve]
        public ClientMatchSubEntry(
            INavigator navigator)
        {
            _navigator = navigator;
        }
        
        public override IDisposable Entry()
        {
            _navigator.Show<MatchScreen>();
            return DeferredAction.Empty;
        }
    }
}
#endif