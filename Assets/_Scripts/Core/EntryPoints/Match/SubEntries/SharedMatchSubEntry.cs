using System;
using System.Collections.Generic;
using System.Linq;
using Core.Disposable;
using Core.Logger;
using Core.Match.Contexts;
using Core.Match.NetworkManager;
using Core.Match.Player;
using Core.Match.Systems;
using Core.Match.Systems.Server.Spawn;
using UnityEngine;
using UnityEngine.Scripting;

namespace Core.EntryPoints.Match.SubEntries
{
    [Preserve]
    public class SharedMatchSubEntry : MatchSubEntry
    {
        private static readonly Log _log = new (nameof(SharedMatchSubEntry));
        private readonly ServerMatchContextController _serverMatchContextController;

        public bool Initialized { get; private set; }

        [Preserve]
        public SharedMatchSubEntry(
            ServerMatchContextController serverMatchContextController)
        {
            _serverMatchContextController = serverMatchContextController;
        }
        
        public override IDisposable Entry()
        {
            CustomNetworkManager.Instance.StartHost();
            
            var playersSubscription = CustomNetworkManager.Instance.Players.Listen(OnPlayersChange);
            return new DeferredAction().Add(() => playersSubscription.Dispose());
        }

        private void OnPlayersChange(IReadOnlyList<NetPlayer> players)
        {
            if (players.Count < CustomNetworkManager.MaxPlayerCount)
                return;

            InitHost();
        }

        private void InitHost()
        {
            if (Initialized)
            {
                _log.Error("Double initialization detected");
                return;
            }
            
            var matchId = Guid.NewGuid();
            
            var matchContext = InitMatchContext(matchId);
            
            var serverSystemContext = new ServerMatchSystemContext(matchContext);
            var serverSystemExecutor = new ServerMatchSystemsExecutor(serverSystemContext);
            
            serverSystemExecutor.Reg<SpawnServerMatchSystem>();
            serverSystemExecutor.ExecuteAllOrdered();
            
            foreach (var player in matchContext.Players)
            {
                player.Match.matchId = matchId;
                player.ReadyUp();
            }
            
            Initialized = true;
        }

        private ServerMatchContext InitMatchContext(Guid matchId)
        {
            var players = CustomNetworkManager.Instance.Players.Value;

            var matchContext = new ServerMatchContext
            {
                Players = players
            };
            
            _serverMatchContextController.Register(matchId, matchContext);
            return matchContext;
        }
    }
}