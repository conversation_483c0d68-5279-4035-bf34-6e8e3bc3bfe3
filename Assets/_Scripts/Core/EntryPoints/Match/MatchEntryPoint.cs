using System;
using Core.Disposable;
using Core.EntryPoints.Match.SubEntries;
using Core.Extensions;
using Core.Logger;
using Core.Match.Disconnection;
using Core.Match.NetworkManager;
using Core.Scenes;
using Cysharp.Threading.Tasks;
using Mirror;
using UnityEngine;
using Zenject;

namespace Core.EntryPoints.Match
{
    public class MatchEntryPoint : MonoBehaviour
    {
        private static readonly Log _log = new (nameof(MatchEntryPoint));
        
        private ISceneNavigator _sceneNavigator;
        private IMatchDisconnector _matchDisconnector;
        private DiContainer _scope;

        private DeferredAction _entries = null!;
        
        [Inject]
        public void In(
            ISceneNavigator sceneNavigator,
            IMatchDisconnector matchDisconnector,
            DiContainer scope)
        {
            _sceneNavigator = sceneNavigator;
            _matchDisconnector = matchDisconnector;
            _scope = scope;
        }

        public void Start()
        {
            StartAsync().Forget();
        }

        private void OnDestroy()
        {
            _entries?.Dispose();
        }

        private async UniTaskVoid StartAsync()
        {
            // var parameters = _sceneNavigator.GetParameters<MatchSceneParams>(SceneType.Match);
            // if (parameters == null)
            // {
            //     _log.Error("Match scene parameters are not set");
            //     _matchDisconnector.Disconnect(MatchDisconnectionReasons.BadTransition);
            // }

            _entries = new DeferredAction();
            
            var shared = EntryTo<SharedMatchSubEntry>();
            await UniTask.WaitUntil(() => shared.Initialized);
#if DEDICATED_SERVER
            EntryTo<DedicatedServerSubEntry>();
#else
            if (NetworkClient.active)
                EntryTo<ClientMatchSubEntry>();
#endif
        }

        private T EntryTo<T>()
            where T : MatchSubEntry
        {
            var entry = _scope.Instantiate<T>();
            var cancellation = entry.Entry();
            _entries.Add(cancellation.Dispose);
            return entry;
        }
    }
}