using Core.Match.Contexts;
using Core.Match.Disconnection;
using Zenject;

namespace Core.EntryPoints.Match
{
    public class MatchInstaller : MonoInstaller<MatchInstaller>
    {
        public override void InstallBindings()
        {
            var scope = Container;
            scope.Bind<IMatchDisconnector>().To<MatchDisconnector>().AsSingle();
            scope.Bind<IServerMatchContextController>().To<ServerMatchContextController>().AsSingle();
            scope.Bind<IClientMatchContextController>().To<ClientMatchContextController>().AsSingle();
        }
    }
}