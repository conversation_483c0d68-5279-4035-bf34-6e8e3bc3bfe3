using _MetaApi;
using Core.Scenes;
using Cysharp.Threading.Tasks;
using Screens.Loading.Management;
using UnityEngine;
using Zenject;

namespace Core.EntryPoints.Init
{
    public class InitEntryPoint : MonoBehaviour
    {
        private ISceneNavigator _sceneNavigator = null!;
        private IUserApiContext _userApiContext = null!;
        private ILoadingManager _loadingManager = null!;
        
        [Inject]
        public void In(
            ISceneNavigator sceneNavigator,
            IUserApiContext userApiContext,
            ILoadingManager loadingManager)
        {
            _sceneNavigator = sceneNavigator;
            _userApiContext = userApiContext;
            _loadingManager = loadingManager;
        }

        public void Start() => StartAsync().Forget();

        private async UniTaskVoid StartAsync()
        {
            var loadingHandler = _loadingManager.Show();
            loadingHandler.SetProgress(0.2f);
            
            //await _userApiContext.InitAsync();
            loadingHandler.SetProgress(1f);
            
            await _sceneNavigator.ChangeAsync(SceneType.Menu);
            loadingHandler.Dispose();
        }
    }
}