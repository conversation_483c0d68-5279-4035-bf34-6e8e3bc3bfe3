using _MetaApi;
using Core.Scenes;
using Cysharp.Threading.Tasks;
using Screens.Loading.Management;
using VContainer;
using VContainer.Unity;

namespace Core.EntryPoints.Init
{
    [Preserve]
    public class InitEntryPoint : IStartable
    {
        private readonly ISceneNavigator _sceneNavigator;
        private readonly IUserApiContext _userApiContext;
        private readonly ILoadingManager _loadingManager;
        
        [Preserve]
        public InitEntryPoint(
            ISceneNavigator sceneNavigator,
            IUserApiContext userApiContext,
            ILoadingManager loadingManager)
        {
            _sceneNavigator = sceneNavigator;
            _userApiContext = userApiContext;
            _loadingManager = loadingManager;
        }

        public void Start() => StartAsync().Forget();

        private async UniTaskVoid StartAsync()
        {
            var loadingHandler = _loadingManager.Show();
            loadingHandler.SetProgress(0.2f);
            
            //await _userApiContext.InitAsync();
            loadingHandler.SetProgress(1f);
            
            await _sceneNavigator.ChangeAsync(SceneType.Menu);
            loadingHandler.Dispose();
        }
    }
}