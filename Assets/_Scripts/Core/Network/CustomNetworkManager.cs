using System;
using System.Collections.Generic;
using Core.Logger;
using Core.Match.Player;
using Core.Network.Auth;
using Core.Network.MatchInterest;
using Core.Network.Messages;
using Mirror;
using UnityEngine;

namespace Core.Network
{
    public class TileBattleCraftNetworkManager : NetworkManager
    {
        private static readonly Log _log = new Log(nameof(TileBattleCraftNetworkManager));
        
        private const int MaxPlayersPerMatch = 2;
        
        [SerializeField]
        private MatchInterestManager _matchInterestManager = null!;
        
        private readonly List<NetPlayer> _spawnedPlayers = new();
        
        public System.Action<NetPlayer> OnPlayerSpawned;
        public System.Action<NetPlayer> OnPlayerDespawned;
        

        public override void OnStartServer()
        {
            base.OnStartServer();
            
            NetworkServer.RegisterHandler<ClientReadyMessage>(OnClientReadyMessage);
        }

        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            var authData = GetPlayerAuthData(conn);
            if (authData == null)
            {
                throw new Exception("No auth data provided");
                Debug.LogError($"[TileBattleCraft] No auth data for connection {conn.connectionId}");
                conn.Disconnect();
                return;
            }

            // Проверяем возможность присоединения к матчу
            if (_matchInterestManager != null && !_matchInterestManager.CanJoinMatch(authData.MatchId))
            {
                Debug.LogWarning($"[TileBattleCraft] Cannot join match {authData.MatchId}, disconnecting client {conn.connectionId}");
                conn.Disconnect();
                return;
            }

            // Создаем игрока
            GameObject playerObject = CreateNetPlayer(conn, authData);
            if (playerObject != null)
            {
                NetworkServer.AddPlayerForConnection(conn, playerObject);

                // Добавляем игрока в матч
                NetPlayer netPlayer = playerObject.GetComponent<NetPlayer>();
                if (_matchInterestManager != null && netPlayer != null)
                {
                    _matchInterestManager.AddPlayerToMatch(netPlayer, authData.MatchId);
                }

                Debug.Log($"[TileBattleCraft] Player {authData.Nickname} added to match {authData.MatchId}");
            }
            else
            {
                Debug.LogError("[TileBattleCraft] Failed to create player object");
                conn.Disconnect();
            }
        }

        public override void OnServerDisconnect(NetworkConnectionToClient conn)
        {
            if (conn.identity != null)
            {
                var netPlayer = conn.identity.GetComponent<NetPlayer>();
                if (netPlayer != null)
                {
                    // Удаляем игрока из матча
                    if (_matchInterestManager != null)
                    {
                        _matchInterestManager.RemovePlayerFromMatch(netPlayer);
                    }

                    RemoveNetPlayer(netPlayer);
                }
            }

            base.OnServerDisconnect(conn);
            Debug.Log($"[TileBattleCraft] Client disconnected: {conn.connectionId}");
        }

        #region Client Events

        public override void OnStartClient()
        {
            base.OnStartClient();

            // Регистрируем обработчики сообщений на клиенте
            NetworkClient.RegisterHandler<MatchStateMessage>(OnMatchStateMessage);
            NetworkClient.RegisterHandler<MatchStartMessage>(OnMatchStartMessage);
            NetworkClient.RegisterHandler<MatchEndMessage>(OnMatchEndMessage);

            Debug.Log("[TileBattleCraft] Client started");
        }

        public override void OnClientConnect()
        {
            base.OnClientConnect();
            Debug.Log("[TileBattleCraft] Connected to server");
        }

        public override void OnClientDisconnect()
        {
            base.OnClientDisconnect();
            Debug.Log("[TileBattleCraft] Disconnected from server");
        }

        #endregion

        #region Player Management
        
        /// <summary>
        /// Создает NetPlayer для подключившегося клиента с аутентификацией
        /// </summary>
        private GameObject CreateNetPlayer(NetworkConnectionToClient conn, PlayerAuthData authData)
        {
            GameObject playerObject;

            if (netPlayerPrefab != null)
            {
                // Используем префаб если он назначен
                playerObject = Instantiate(netPlayerPrefab);
            }
            else
            {
                // Создаем объект вручную
                playerObject = new GameObject($"NetPlayer [connId={conn.connectionId}]", typeof(NetPlayer));
            }

            playerObject.name = $"NetPlayer [{authData.Nickname}] [connId={conn.connectionId}]";

            var netPlayer = playerObject.GetComponent<NetPlayer>();
            if (netPlayer == null)
            {
                Debug.LogError("[TileBattleCraft] NetPlayer component not found on prefab!");
                Destroy(playerObject);
                return null;
            }

            // Сохраняем данные аутентификации в соединении
            conn.authenticationData = authData;

            _spawnedPlayers.Add(netPlayer);
            OnPlayerSpawned?.Invoke(netPlayer);

            return playerObject;
        }

        /// <summary>
        /// Получает данные аутентификации игрока
        /// </summary>
        private PlayerAuthData GetPlayerAuthData(NetworkConnectionToClient conn)
        {
            // Пока что возвращаем тестовые данные
            // В реальном проекте это должно приходить от клиента при подключении
            if (conn.authenticationData is PlayerAuthData authData)
            {
                return authData;
            }

            // Создаем тестовые данные если их нет
            var testAuthData = new PlayerAuthData($"Player_{conn.connectionId}", 0);
            conn.authenticationData = testAuthData;
            return testAuthData;
        }

        /// <summary>
        /// Устанавливает данные аутентификации для соединения
        /// </summary>
        public void SetPlayerAuthData(NetworkConnectionToClient conn, PlayerAuthData authData)
        {
            conn.authenticationData = authData;
            Debug.Log($"[TileBattleCraft] Set auth data for connection {conn.connectionId}: {authData}");
        }

        /// <summary>
        /// Удаляет NetPlayer из списка
        /// </summary>
        private void RemoveNetPlayer(NetPlayer netPlayer)
        {
            if (_spawnedPlayers.Remove(netPlayer))
            {
                OnPlayerDespawned?.Invoke(netPlayer);
                Debug.Log($"[TileBattleCraft] NetPlayer removed: {netPlayer.name}");
            }
        }

        /// <summary>
        /// Получает всех созданных игроков
        /// </summary>
        public List<NetPlayer> GetSpawnedPlayers()
        {
            return new List<NetPlayer>(_spawnedPlayers);
        }

        /// <summary>
        /// Получает игрока по индексу (0 - первый, 1 - второй)
        /// </summary>
        public NetPlayer GetPlayerByIndex(int index)
        {
            if (index >= 0 && index < _spawnedPlayers.Count) return _spawnedPlayers[index];
            return null;
        }

        #endregion

        #region Message Handlers

        /// <summary>
        /// Обработчик сообщения готовности клиента (на сервере)
        /// </summary>
        private void OnClientReadyMessage(NetworkConnectionToClient conn, ClientReadyMessage message)
        {
            Debug.Log($"[TileBattleCraft] Client {conn.connectionId} ready state: {message.isReady}");

            // Здесь можно добавить логику обработки готовности игрока
            // Например, уведомить систему состояния матча
        }

        /// <summary>
        /// Обработчик сообщения состояния матча (на клиенте)
        /// </summary>
        private void OnMatchStateMessage(MatchStateMessage message)
        {
            Debug.Log(
                $"[TileBattleCraft] Match state: {message.matchState}, Players: {message.playerCount}/{message.maxPlayers}");

            // Здесь можно обновить UI или уведомить другие системы
        }

        /// <summary>
        /// Обработчик сообщения начала матча (на клиенте)
        /// </summary>
        private void OnMatchStartMessage(MatchStartMessage message)
        {
            Debug.Log($"[TileBattleCraft] Match started at time: {message.startTime}");

            // Здесь можно запустить игровую логику на клиенте
        }

        /// <summary>
        /// Обработчик сообщения завершения матча (на клиенте)
        /// </summary>
        private void OnMatchEndMessage(MatchEndMessage message)
        {
            Debug.Log($"[TileBattleCraft] Match ended. Winner: {message.winnerId}, Reason: {message.reason}");

            // Здесь можно показать результаты матча
        }

        /// <summary>
        /// Отправляет состояние матча всем клиентам
        /// </summary>
        public void BroadcastMatchState(MatchStateMessage.State state)
        {
            if (!NetworkServer.active) return;

            var message = new MatchStateMessage
            {
                matchState = state,
                playerCount = _spawnedPlayers.Count,
                maxPlayers = MaxPlayersPerMatch
            };

            NetworkServer.SendToAll(message);
        }

        /// <summary>
        /// Отправляет сообщение о начале матча всем клиентам
        /// </summary>
        public void BroadcastMatchStart()
        {
            if (!NetworkServer.active) return;

            var message = new MatchStartMessage
            {
                startTime = (float)NetworkTime.time
            };

            NetworkServer.SendToAll(message);
        }

        /// <summary>
        /// Отправляет сообщение о завершении матча всем клиентам
        /// </summary>
        public void BroadcastMatchEnd(int winnerId, string reason)
        {
            if (!NetworkServer.active) return;

            var message = new MatchEndMessage
            {
                winnerId = winnerId,
                reason = reason
            };

            NetworkServer.SendToAll(message);
        }

        #endregion
    }
}