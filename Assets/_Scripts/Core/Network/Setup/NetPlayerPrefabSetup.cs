using Core.Match.Player;
using Mirror;
using UnityEngine;

namespace Core.Network.Setup
{
    /// <summary>
    /// Утилита для настройки префаба NetPlayer
    /// Автоматически добавляет необходимые компоненты
    /// </summary>
    [System.Serializable]
    public class NetPlayerPrefabSetup
    {
        /// <summary>
        /// Создает и настраивает префаб NetPlayer
        /// </summary>
        public static GameObject CreateNetPlayerPrefab(string prefabName = "NetPlayer")
        {
            // Создаем основной GameObject
            GameObject playerObject = new GameObject(prefabName);
            
            // Добавляем NetworkIdentity (обязательно для всех сетевых объектов)
            NetworkIdentity networkIdentity = playerObject.AddComponent<NetworkIdentity>();
            
            // Настраиваем NetworkIdentity
            networkIdentity.serverOnly = false; // Объект существует и на сервере, и на клиенте
            
            // Добавляем основной компонент NetPlayer
            NetPlayer netPlayer = playerObject.AddComponent<NetPlayer>();
            
            // Добавляем визуальное представление (опционально)
            AddVisualRepresentation(playerObject);
            
            Debug.Log($"[NetPlayerPrefabSetup] Created NetPlayer prefab: {prefabName}");
            
            return playerObject;
        }
        
        /// <summary>
        /// Добавляет визуальное представление игрока
        /// </summary>
        private static void AddVisualRepresentation(GameObject playerObject)
        {
            // Создаем простой куб как визуальное представление
            GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cube);
            visual.name = "Visual";
            visual.transform.SetParent(playerObject.transform);
            visual.transform.localPosition = Vector3.zero;
            visual.transform.localScale = Vector3.one;
            
            // Добавляем материал для различения игроков
            Renderer renderer = visual.GetComponent<Renderer>();
            if (renderer != null)
            {
                // Создаем простой материал
                Material playerMaterial = new Material(Shader.Find("Standard"));
                playerMaterial.color = Color.blue;
                renderer.material = playerMaterial;
            }
            
            // Удаляем коллайдер с визуального объекта (если нужен коллайдер, добавим отдельно)
            Collider visualCollider = visual.GetComponent<Collider>();
            if (visualCollider != null)
            {
                Object.DestroyImmediate(visualCollider);
            }
        }
        
        /// <summary>
        /// Проверяет корректность настройки префаба NetPlayer
        /// </summary>
        public static bool ValidateNetPlayerPrefab(GameObject prefab)
        {
            if (prefab == null)
            {
                Debug.LogError("[NetPlayerPrefabSetup] Prefab is null");
                return false;
            }
            
            // Проверяем наличие NetworkIdentity
            NetworkIdentity networkIdentity = prefab.GetComponent<NetworkIdentity>();
            if (networkIdentity == null)
            {
                Debug.LogError($"[NetPlayerPrefabSetup] {prefab.name} missing NetworkIdentity component");
                return false;
            }
            
            // Проверяем наличие NetPlayer
            NetPlayer netPlayer = prefab.GetComponent<NetPlayer>();
            if (netPlayer == null)
            {
                Debug.LogError($"[NetPlayerPrefabSetup] {prefab.name} missing NetPlayer component");
                return false;
            }
            
            Debug.Log($"[NetPlayerPrefabSetup] {prefab.name} validation passed");
            return true;
        }
        
        /// <summary>
        /// Автоматически настраивает существующий GameObject как NetPlayer
        /// </summary>
        public static bool SetupExistingGameObject(GameObject gameObject)
        {
            if (gameObject == null)
            {
                Debug.LogError("[NetPlayerPrefabSetup] GameObject is null");
                return false;
            }
            
            // Добавляем NetworkIdentity если его нет
            NetworkIdentity networkIdentity = gameObject.GetComponent<NetworkIdentity>();
            if (networkIdentity == null)
            {
                networkIdentity = gameObject.AddComponent<NetworkIdentity>();
                Debug.Log($"[NetPlayerPrefabSetup] Added NetworkIdentity to {gameObject.name}");
            }
            
            // Добавляем NetPlayer если его нет
            NetPlayer netPlayer = gameObject.GetComponent<NetPlayer>();
            if (netPlayer == null)
            {
                netPlayer = gameObject.AddComponent<NetPlayer>();
                Debug.Log($"[NetPlayerPrefabSetup] Added NetPlayer to {gameObject.name}");
            }
            
            // Настраиваем NetworkIdentity
            networkIdentity.serverOnly = false;
            
            Debug.Log($"[NetPlayerPrefabSetup] Successfully setup {gameObject.name} as NetPlayer");
            return true;
        }
    }
    
    /// <summary>
    /// Editor утилита для создания префабов NetPlayer
    /// </summary>
    #if UNITY_EDITOR
    [UnityEditor.CustomEditor(typeof(NetPlayerPrefabSetup))]
    public class NetPlayerPrefabSetupEditor : UnityEditor.Editor
    {
        public override void OnInspectorGUI()
        {
            GUILayout.Label("NetPlayer Prefab Setup Utility", UnityEditor.EditorStyles.boldLabel);
            GUILayout.Space(10);
            
            if (GUILayout.Button("Create NetPlayer Prefab"))
            {
                GameObject prefab = NetPlayerPrefabSetup.CreateNetPlayerPrefab();
                UnityEditor.Selection.activeGameObject = prefab;
            }
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Setup Selected GameObject"))
            {
                GameObject selected = UnityEditor.Selection.activeGameObject;
                if (selected != null)
                {
                    NetPlayerPrefabSetup.SetupExistingGameObject(selected);
                }
                else
                {
                    UnityEditor.EditorUtility.DisplayDialog("Error", "Please select a GameObject first", "OK");
                }
            }
            
            GUILayout.Space(10);
            
            if (GUILayout.Button("Validate Selected Prefab"))
            {
                GameObject selected = UnityEditor.Selection.activeGameObject;
                if (selected != null)
                {
                    bool isValid = NetPlayerPrefabSetup.ValidateNetPlayerPrefab(selected);
                    string message = isValid ? "Prefab is valid!" : "Prefab validation failed!";
                    UnityEditor.EditorUtility.DisplayDialog("Validation Result", message, "OK");
                }
                else
                {
                    UnityEditor.EditorUtility.DisplayDialog("Error", "Please select a GameObject first", "OK");
                }
            }
        }
    }
    #endif
}
