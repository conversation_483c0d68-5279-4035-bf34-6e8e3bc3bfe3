using Core.Match.Systems.Server.MatchState;
using Core.Network.Client;
using Core.Network.Messages;
using Mirror;
using UnityEngine;

namespace Core.Network.Integration
{
    /// <summary>
    /// Интегратор мультиплеера - связывает все сетевые системы вместе
    /// Обеспечивает взаимодействие между клиентом, сервером и системами матча
    /// </summary>
    public class MultiplayerIntegrator : MonoBehaviour
    {
        [Header("References")]
        [Tooltip("NetworkManager для управления сетью")]
        public TileBattleCraftNetworkManager networkManager;
        
        [Tooltip("Контроллер клиента")]
        public NetworkClientController clientController;
        
        [Head<PERSON>("Setting<PERSON>")]
        [Tooltip("Автоматически запускать интеграцию при старте")]
        public bool autoInitialize = true;
        
        // Ссылки на системы
        private MatchStateServerSystem _matchStateSystem;
        
        // События интеграции
        public System.Action<bool> OnServerStateChanged;
        public System.Action<bool> OnClientStateChanged;
        public System.Action<MatchStateServerSystem.MatchState> OnMatchStateChanged;
        
        private void Start()
        {
            if (autoInitialize)
            {
                Initialize();
            }
        }
        
        /// <summary>
        /// Инициализирует интегратор мультиплеера
        /// </summary>
        public void Initialize()
        {
            Debug.Log("[MultiplayerIntegrator] Initializing multiplayer integration");
            
            // Находим компоненты если не назначены
            FindComponents();
            
            // Настраиваем интеграцию
            SetupIntegration();
            
            Debug.Log("[MultiplayerIntegrator] Multiplayer integration initialized");
        }
        
        /// <summary>
        /// Находит необходимые компоненты
        /// </summary>
        private void FindComponents()
        {
            if (networkManager == null)
            {
                networkManager = FindObjectOfType<TileBattleCraftNetworkManager>();
                if (networkManager == null)
                {
                    Debug.LogError("[MultiplayerIntegrator] TileBattleCraftNetworkManager not found!");
                    return;
                }
            }
            
            if (clientController == null)
            {
                clientController = FindObjectOfType<NetworkClientController>();
                if (clientController == null)
                {
                    Debug.LogWarning("[MultiplayerIntegrator] NetworkClientController not found - client features will be limited");
                }
            }
        }
        
        /// <summary>
        /// Настраивает интеграцию между системами
        /// </summary>
        private void SetupIntegration()
        {
            if (networkManager == null) return;
            
            // Подписываемся на события NetworkManager
            networkManager.OnPlayerSpawned += OnPlayerSpawned;
            networkManager.OnPlayerDespawned += OnPlayerDespawned;
            
            // Настраиваем клиентский контроллер
            if (clientController != null)
            {
                clientController.OnConnectedToServer += OnClientConnected;
                clientController.OnDisconnectedFromServer += OnClientDisconnected;
                clientController.OnMatchStateChanged += OnClientMatchStateChanged;
                clientController.OnMatchStarted += OnClientMatchStarted;
                clientController.OnMatchEnded += OnClientMatchEnded;
            }
        }
        
        private void OnDestroy()
        {
            // Отписываемся от событий
            if (networkManager != null)
            {
                networkManager.OnPlayerSpawned -= OnPlayerSpawned;
                networkManager.OnPlayerDespawned -= OnPlayerDespawned;
            }
            
            if (clientController != null)
            {
                clientController.OnConnectedToServer -= OnClientConnected;
                clientController.OnDisconnectedFromServer -= OnClientDisconnected;
                clientController.OnMatchStateChanged -= OnClientMatchStateChanged;
                clientController.OnMatchStarted -= OnClientMatchStarted;
                clientController.OnMatchEnded -= OnClientMatchEnded;
            }
        }
        
        #region Server Events
        
        private void OnPlayerSpawned(Core.Match.Player.NetPlayer player)
        {
            Debug.Log($"[MultiplayerIntegrator] Player spawned: {player.name}");
            
            // Уведомляем клиентов об изменении состояния
            if (NetworkServer.active)
            {
                BroadcastPlayerCount();
            }
        }
        
        private void OnPlayerDespawned(Core.Match.Player.NetPlayer player)
        {
            Debug.Log($"[MultiplayerIntegrator] Player despawned: {player.name}");
            
            // Уведомляем клиентов об изменении состояния
            if (NetworkServer.active)
            {
                BroadcastPlayerCount();
            }
        }
        
        /// <summary>
        /// Отправляет информацию о количестве игроков всем клиентам
        /// </summary>
        private void BroadcastPlayerCount()
        {
            if (networkManager == null) return;
            
            var players = networkManager.GetSpawnedPlayers();
            MatchStateMessage.State currentState = MatchStateMessage.State.WaitingForPlayers;
            
            if (players.Count >= 2)
            {
                currentState = MatchStateMessage.State.PlayersReady;
            }
            
            networkManager.BroadcastMatchState(currentState);
        }
        
        #endregion
        
        #region Client Events
        
        private void OnClientConnected()
        {
            Debug.Log("[MultiplayerIntegrator] Client connected to server");
            OnClientStateChanged?.Invoke(true);
        }
        
        private void OnClientDisconnected()
        {
            Debug.Log("[MultiplayerIntegrator] Client disconnected from server");
            OnClientStateChanged?.Invoke(false);
        }
        
        private void OnClientMatchStateChanged(MatchStateMessage.State state)
        {
            Debug.Log($"[MultiplayerIntegrator] Client received match state: {state}");
            
            // Конвертируем в серверный тип состояния
            MatchStateServerSystem.MatchState serverState = ConvertToServerState(state);
            OnMatchStateChanged?.Invoke(serverState);
        }
        
        private void OnClientMatchStarted()
        {
            Debug.Log("[MultiplayerIntegrator] Client received match start");
        }
        
        private void OnClientMatchEnded(int winnerId, string reason)
        {
            Debug.Log($"[MultiplayerIntegrator] Client received match end: Winner {winnerId}, Reason: {reason}");
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Конвертирует состояние сообщения в серверное состояние
        /// </summary>
        private MatchStateServerSystem.MatchState ConvertToServerState(MatchStateMessage.State messageState)
        {
            switch (messageState)
            {
                case MatchStateMessage.State.WaitingForPlayers:
                    return MatchStateServerSystem.MatchState.WaitingForPlayers;
                case MatchStateMessage.State.PlayersReady:
                    return MatchStateServerSystem.MatchState.PlayersReady;
                case MatchStateMessage.State.InProgress:
                    return MatchStateServerSystem.MatchState.InProgress;
                case MatchStateMessage.State.Finished:
                    return MatchStateServerSystem.MatchState.Finished;
                default:
                    return MatchStateServerSystem.MatchState.WaitingForPlayers;
            }
        }
        
        /// <summary>
        /// Проверяет, активен ли сервер
        /// </summary>
        public bool IsServerActive()
        {
            return NetworkServer.active;
        }
        
        /// <summary>
        /// Проверяет, подключен ли клиент
        /// </summary>
        public bool IsClientConnected()
        {
            return NetworkClient.active && NetworkClient.isConnected;
        }
        
        /// <summary>
        /// Получает количество подключенных игроков
        /// </summary>
        public int GetPlayerCount()
        {
            if (networkManager != null && NetworkServer.active)
            {
                return networkManager.GetSpawnedPlayers().Count;
            }
            return 0;
        }
        
        /// <summary>
        /// Запускает сервер
        /// </summary>
        public void StartServer()
        {
            if (networkManager != null)
            {
                networkManager.StartServer();
                OnServerStateChanged?.Invoke(true);
            }
        }
        
        /// <summary>
        /// Запускает клиент
        /// </summary>
        public void StartClient(string address = null)
        {
            if (networkManager != null)
            {
                if (!string.IsNullOrEmpty(address))
                {
                    networkManager.networkAddress = address;
                }
                networkManager.StartClient();
            }
        }
        
        /// <summary>
        /// Запускает хост (сервер + клиент)
        /// </summary>
        public void StartHost()
        {
            if (networkManager != null)
            {
                networkManager.StartHost();
                OnServerStateChanged?.Invoke(true);
                OnClientStateChanged?.Invoke(true);
            }
        }
        
        /// <summary>
        /// Останавливает сеть
        /// </summary>
        public void StopNetwork()
        {
            if (networkManager != null)
            {
                if (NetworkServer.active && NetworkClient.active)
                    networkManager.StopHost();
                else if (NetworkServer.active)
                    networkManager.StopServer();
                else if (NetworkClient.active)
                    networkManager.StopClient();
                    
                OnServerStateChanged?.Invoke(false);
                OnClientStateChanged?.Invoke(false);
            }
        }
        
        #endregion
    }
}
