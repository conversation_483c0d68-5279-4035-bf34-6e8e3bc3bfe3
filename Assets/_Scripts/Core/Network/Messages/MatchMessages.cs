using Mirror;

namespace Core.Network.Messages
{
    /// <summary>
    /// Сообщение о готовности клиента к матчу
    /// </summary>
    public struct ClientReadyMessage : NetworkMessage
    {
        public bool isReady;
    }
    
    /// <summary>
    /// Сообщение о состоянии матча от сервера
    /// </summary>
    public struct MatchStateMessage : NetworkMessage
    {
        public enum State
        {
            WaitingForPlayers,
            PlayersReady,
            InProgress,
            Finished
        }
        
        public State matchState;
        public int playerCount;
        public int maxPlayers;
    }
    
    /// <summary>
    /// Сообщение о начале матча
    /// </summary>
    public struct MatchStartMessage : NetworkMessage
    {
        public float startTime;
    }
    
    /// <summary>
    /// Сообщение о завершении матча
    /// </summary>
    public struct MatchEndMessage : NetworkMessage
    {
        public int winnerId;
        public string reason;
    }
    
    /// <summary>
    /// Сообщение с информацией об игроке
    /// </summary>
    public struct PlayerInfoMessage : NetworkMessage
    {
        public uint playerId;
        public string playerName;
        public bool isReady;
    }
}
