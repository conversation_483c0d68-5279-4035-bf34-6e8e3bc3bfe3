using System.Collections.Generic;
using Core.Match.Player;
using Core.Network.Messages;
using Mirror;
using UnityEngine;

namespace Core.Network
{
    /// <summary>
    /// Кастомный NetworkManager для TileBattleCraft
    /// Управляет подключениями игроков и созданием NetPlayer объектов
    /// </summary>
    [AddComponentMenu("")]
    public class TileBattleCraftNetworkManager : NetworkManager
    {
        [Header("TileBattleCraft Settings")]
        [Tooltip("Префаб NetPlayer для создания игроков")]
        public GameObject netPlayerPrefab;
        
        [Tooltip("Максимальное количество игроков в матче")]
        public int maxPlayersPerMatch = 2;
        
        // Список созданных игроков
        private readonly List<NetPlayer> _spawnedPlayers = new List<NetPlayer>();
        
        // События для уведомления других систем
        public System.Action<NetPlayer> OnPlayerSpawned;
        public System.Action<NetPlayer> OnPlayerDespawned;
        
        public override void Awake()
        {
            base.Awake();
            
            // Устанавливаем префаб игрока если не установлен
            if (playerPrefab == null && netPlayerPrefab != null)
            {
                playerPrefab = netPlayerPrefab;
            }
        }
        
        #region Server Events
        
        public override void OnStartServer()
        {
            base.OnStartServer();

            // Регистрируем обработчики сообщений на сервере
            NetworkServer.RegisterHandler<ClientReadyMessage>(OnClientReadyMessage);

            Debug.Log("[TileBattleCraft] Server started");
        }
        
        public override void OnServerConnect(NetworkConnectionToClient conn)
        {
            base.OnServerConnect(conn);
            Debug.Log($"[TileBattleCraft] Client connected: {conn.connectionId}");
        }
        
        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            // Проверяем лимит игроков
            if (numPlayers >= maxPlayersPerMatch)
            {
                Debug.LogWarning($"[TileBattleCraft] Max players reached ({maxPlayersPerMatch}), disconnecting client {conn.connectionId}");
                conn.Disconnect();
                return;
            }
            
            // Создаем NetPlayer
            GameObject playerObject = CreateNetPlayer(conn);
            if (playerObject != null)
            {
                NetworkServer.AddPlayerForConnection(conn, playerObject);
                Debug.Log($"[TileBattleCraft] Player added for connection {conn.connectionId}");
            }
            else
            {
                Debug.LogError("[TileBattleCraft] Failed to create player object");
                conn.Disconnect();
            }
        }
        
        public override void OnServerDisconnect(NetworkConnectionToClient conn)
        {
            // Удаляем игрока из списка
            if (conn.identity != null)
            {
                NetPlayer netPlayer = conn.identity.GetComponent<NetPlayer>();
                if (netPlayer != null)
                {
                    RemoveNetPlayer(netPlayer);
                }
            }
            
            base.OnServerDisconnect(conn);
            Debug.Log($"[TileBattleCraft] Client disconnected: {conn.connectionId}");
        }
        
        #endregion
        
        #region Client Events
        
        public override void OnStartClient()
        {
            base.OnStartClient();

            // Регистрируем обработчики сообщений на клиенте
            NetworkClient.RegisterHandler<MatchStateMessage>(OnMatchStateMessage);
            NetworkClient.RegisterHandler<MatchStartMessage>(OnMatchStartMessage);
            NetworkClient.RegisterHandler<MatchEndMessage>(OnMatchEndMessage);

            Debug.Log("[TileBattleCraft] Client started");
        }
        
        public override void OnClientConnect()
        {
            base.OnClientConnect();
            Debug.Log("[TileBattleCraft] Connected to server");
        }
        
        public override void OnClientDisconnect()
        {
            base.OnClientDisconnect();
            Debug.Log("[TileBattleCraft] Disconnected from server");
        }
        
        #endregion
        
        #region Player Management
        
        /// <summary>
        /// Создает NetPlayer для подключившегося клиента
        /// </summary>
        private GameObject CreateNetPlayer(NetworkConnectionToClient conn)
        {
            if (netPlayerPrefab == null)
            {
                Debug.LogError("[TileBattleCraft] NetPlayer prefab is not assigned!");
                return null;
            }
            
            // Создаем объект игрока
            GameObject playerObject = Instantiate(netPlayerPrefab);
            playerObject.name = $"NetPlayer [connId={conn.connectionId}]";
            
            // Получаем компонент NetPlayer
            NetPlayer netPlayer = playerObject.GetComponent<NetPlayer>();
            if (netPlayer == null)
            {
                Debug.LogError("[TileBattleCraft] NetPlayer component not found on prefab!");
                Destroy(playerObject);
                return null;
            }
            
            // Добавляем в список
            _spawnedPlayers.Add(netPlayer);
            
            // Уведомляем другие системы
            OnPlayerSpawned?.Invoke(netPlayer);
            
            return playerObject;
        }
        
        /// <summary>
        /// Удаляет NetPlayer из списка
        /// </summary>
        private void RemoveNetPlayer(NetPlayer netPlayer)
        {
            if (_spawnedPlayers.Remove(netPlayer))
            {
                OnPlayerDespawned?.Invoke(netPlayer);
                Debug.Log($"[TileBattleCraft] NetPlayer removed: {netPlayer.name}");
            }
        }
        
        /// <summary>
        /// Получает всех созданных игроков
        /// </summary>
        public List<NetPlayer> GetSpawnedPlayers()
        {
            return new List<NetPlayer>(_spawnedPlayers);
        }
        
        /// <summary>
        /// Получает игрока по индексу (0 - первый, 1 - второй)
        /// </summary>
        public NetPlayer GetPlayerByIndex(int index)
        {
            if (index >= 0 && index < _spawnedPlayers.Count)
            {
                return _spawnedPlayers[index];
            }
            return null;
        }
        
        #endregion

        #region Message Handlers

        /// <summary>
        /// Обработчик сообщения готовности клиента (на сервере)
        /// </summary>
        private void OnClientReadyMessage(NetworkConnectionToClient conn, ClientReadyMessage message)
        {
            Debug.Log($"[TileBattleCraft] Client {conn.connectionId} ready state: {message.isReady}");

            // Здесь можно добавить логику обработки готовности игрока
            // Например, уведомить систему состояния матча
        }

        /// <summary>
        /// Обработчик сообщения состояния матча (на клиенте)
        /// </summary>
        private void OnMatchStateMessage(MatchStateMessage message)
        {
            Debug.Log($"[TileBattleCraft] Match state: {message.matchState}, Players: {message.playerCount}/{message.maxPlayers}");

            // Здесь можно обновить UI или уведомить другие системы
        }

        /// <summary>
        /// Обработчик сообщения начала матча (на клиенте)
        /// </summary>
        private void OnMatchStartMessage(MatchStartMessage message)
        {
            Debug.Log($"[TileBattleCraft] Match started at time: {message.startTime}");

            // Здесь можно запустить игровую логику на клиенте
        }

        /// <summary>
        /// Обработчик сообщения завершения матча (на клиенте)
        /// </summary>
        private void OnMatchEndMessage(MatchEndMessage message)
        {
            Debug.Log($"[TileBattleCraft] Match ended. Winner: {message.winnerId}, Reason: {message.reason}");

            // Здесь можно показать результаты матча
        }

        /// <summary>
        /// Отправляет состояние матча всем клиентам
        /// </summary>
        public void BroadcastMatchState(MatchStateMessage.State state)
        {
            if (!NetworkServer.active) return;

            var message = new MatchStateMessage
            {
                matchState = state,
                playerCount = _spawnedPlayers.Count,
                maxPlayers = maxPlayersPerMatch
            };

            NetworkServer.SendToAll(message);
        }

        /// <summary>
        /// Отправляет сообщение о начале матча всем клиентам
        /// </summary>
        public void BroadcastMatchStart()
        {
            if (!NetworkServer.active) return;

            var message = new MatchStartMessage
            {
                startTime = (float)NetworkTime.time
            };

            NetworkServer.SendToAll(message);
        }

        /// <summary>
        /// Отправляет сообщение о завершении матча всем клиентам
        /// </summary>
        public void BroadcastMatchEnd(int winnerId, string reason)
        {
            if (!NetworkServer.active) return;

            var message = new MatchEndMessage
            {
                winnerId = winnerId,
                reason = reason
            };

            NetworkServer.SendToAll(message);
        }

        #endregion
    }
}
