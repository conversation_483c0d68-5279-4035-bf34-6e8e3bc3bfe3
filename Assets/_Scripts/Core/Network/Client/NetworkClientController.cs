using Core.Network.Messages;
using Mirror;
using UnityEngine;

namespace Core.Network.Client
{
    /// <summary>
    /// Контроллер клиентской части сети
    /// Управляет подключением к серверу и обработкой клиентских событий
    /// </summary>
    public class NetworkClientController : MonoBehaviour
    {
        [Header("Connection Settings")]
        [Tooltip("Адрес сервера для подключения")]
        public string serverAddress = "localhost";
        
        [Tooltip("Порт сервера")]
        public ushort serverPort = 7777;
        
        [Tooltip("Автоматически подключаться при старте")]
        public bool autoConnect = false;
        
        // События клиента
        public System.Action OnConnectedToServer;
        public System.Action OnDisconnectedFromServer;
        public System.Action<MatchStateMessage.State> OnMatchStateChanged;
        public System.Action OnMatchStarted;
        public System.Action<int, string> OnMatchEnded;
        
        private TileBattleCraftNetworkManager _networkManager;
        private bool _isReady = false;
        
        private void Start()
        {
            // Получаем NetworkManager
            _networkManager = FindObjectOfType<TileBattleCraftNetworkManager>();
            if (_networkManager == null)
            {
                Debug.LogError("[NetworkClientController] TileBattleCraftNetworkManager not found!");
                return;
            }
            
            // Устанавливаем адрес сервера
            _networkManager.networkAddress = serverAddress;
            
            // Автоподключение если включено
            if (autoConnect)
            {
                ConnectToServer();
            }
        }
        
        private void OnEnable()
        {
            // Подписываемся на события NetworkManager
            if (_networkManager != null)
            {
                // Эти события будут обрабатываться через переопределенные методы в NetworkManager
            }
        }
        
        private void OnDisable()
        {
            // Отписываемся от событий
        }
        
        /// <summary>
        /// Подключается к серверу
        /// </summary>
        public void ConnectToServer()
        {
            if (NetworkClient.active)
            {
                Debug.LogWarning("[NetworkClientController] Already connected to server");
                return;
            }
            
            Debug.Log($"[NetworkClientController] Connecting to server {serverAddress}:{serverPort}");
            
            // Устанавливаем адрес и подключаемся
            _networkManager.networkAddress = serverAddress;
            _networkManager.StartClient();
        }
        
        /// <summary>
        /// Отключается от сервера
        /// </summary>
        public void DisconnectFromServer()
        {
            if (!NetworkClient.active)
            {
                Debug.LogWarning("[NetworkClientController] Not connected to server");
                return;
            }
            
            Debug.Log("[NetworkClientController] Disconnecting from server");
            _networkManager.StopClient();
        }
        
        /// <summary>
        /// Отправляет сообщение о готовности на сервер
        /// </summary>
        public void SetReady(bool ready)
        {
            if (!NetworkClient.active)
            {
                Debug.LogWarning("[NetworkClientController] Not connected to server");
                return;
            }
            
            _isReady = ready;
            
            var message = new ClientReadyMessage
            {
                isReady = ready
            };
            
            NetworkClient.Send(message);
            Debug.Log($"[NetworkClientController] Sent ready state: {ready}");
        }
        
        /// <summary>
        /// Проверяет, подключен ли клиент к серверу
        /// </summary>
        public bool IsConnected()
        {
            return NetworkClient.active && NetworkClient.isConnected;
        }
        
        /// <summary>
        /// Проверяет, готов ли клиент
        /// </summary>
        public bool IsReady()
        {
            return _isReady;
        }
        
        /// <summary>
        /// Получает текущий ping до сервера
        /// </summary>
        public double GetPing()
        {
            return NetworkTime.rtt * 1000; // Конвертируем в миллисекунды
        }
        
        /// <summary>
        /// Вызывается при подключении к серверу
        /// </summary>
        public void HandleConnectedToServer()
        {
            Debug.Log("[NetworkClientController] Connected to server");
            OnConnectedToServer?.Invoke();
        }
        
        /// <summary>
        /// Вызывается при отключении от сервера
        /// </summary>
        public void HandleDisconnectedFromServer()
        {
            Debug.Log("[NetworkClientController] Disconnected from server");
            _isReady = false;
            OnDisconnectedFromServer?.Invoke();
        }
        
        /// <summary>
        /// Обрабатывает изменение состояния матча
        /// </summary>
        public void HandleMatchStateChanged(MatchStateMessage.State state)
        {
            Debug.Log($"[NetworkClientController] Match state changed: {state}");
            OnMatchStateChanged?.Invoke(state);
        }
        
        /// <summary>
        /// Обрабатывает начало матча
        /// </summary>
        public void HandleMatchStarted()
        {
            Debug.Log("[NetworkClientController] Match started");
            OnMatchStarted?.Invoke();
        }
        
        /// <summary>
        /// Обрабатывает завершение матча
        /// </summary>
        public void HandleMatchEnded(int winnerId, string reason)
        {
            Debug.Log($"[NetworkClientController] Match ended. Winner: {winnerId}, Reason: {reason}");
            OnMatchEnded?.Invoke(winnerId, reason);
        }
        
        #region UI Methods
        
        /// <summary>
        /// Метод для кнопки подключения в UI
        /// </summary>
        public void OnConnectButtonClicked()
        {
            ConnectToServer();
        }
        
        /// <summary>
        /// Метод для кнопки отключения в UI
        /// </summary>
        public void OnDisconnectButtonClicked()
        {
            DisconnectFromServer();
        }
        
        /// <summary>
        /// Метод для кнопки готовности в UI
        /// </summary>
        public void OnReadyButtonClicked()
        {
            SetReady(!_isReady);
        }
        
        #endregion
    }
}
