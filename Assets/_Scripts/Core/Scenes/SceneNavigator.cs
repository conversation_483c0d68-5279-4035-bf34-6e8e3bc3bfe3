using System;
using System.Collections.Generic;
using Core.Rx;
using Cysharp.Threading.Tasks;
using UnityEngine.Scripting;

namespace Core.Scenes
{
    [Preserve]
    public class SceneNavigator : ISceneNavigator
    {
        private readonly Rx<SceneType> _current = new(SceneType.Init);
        private readonly IDictionary<SceneType, object> _lastParameters;

        [Preserve]
        public SceneNavigator()
        {
            _lastParameters = new Dictionary<SceneType, object>();
        }
        
        public IRx<SceneType> Current => _current;

        public T? GetParameters<T>(SceneType type) where T : class
        {
            if (_lastParameters.TryGetValue(type, out var parameters))
                return (T)parameters;
            
            return null;
        }
        
        public async UniTask ChangeAsync(SceneType type, object? parameters = null)
        {
            var asyncLoad = UnityEngine.SceneManagement.SceneManager.LoadSceneAsync(type.ToString());
            if (asyncLoad == null)
                throw new NullReferenceException($"Can't find scene '{type}'");

            UpdateParameters(type, parameters);
            
            // ReSharper disable once HeapView.CanAvoidClosure
            await UniTask.WaitUntil(() => asyncLoad.progress > 0.9f);
            _current.Value = type;
        }
        
        private void UpdateParameters(SceneType type, object? parameters)
        {
            if (parameters == null)
                return;
            
            _lastParameters[type] = parameters;
        }
    }
}