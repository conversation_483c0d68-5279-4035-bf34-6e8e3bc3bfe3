using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Core.Logger;
using VContainer;

namespace Core.Extensions
{
    public static class VContainerExtensions
    {
        private static readonly Log _log = new (nameof(VContainerExtensions));
        private static readonly IDictionary<Type, ConstructorInfo[]> _constructorCache = new Dictionary<Type, ConstructorInfo[]>();
        
        public static T CreateAndInject<T>(this IObjectResolver resolver, Type type)
            where T : class
        {
            if (!_constructorCache.TryGetValue(type, out var constructors))
            {
                constructors = type.GetTypeInfo().DeclaredConstructors.ToArray();
                _constructorCache.Add(type, constructors);
            }
            
            if (constructors.Length == 0)
                return Activator.CreateInstance(type) as T;

            if (constructors.Length > 1)
            {
                _log.Error($"Found '{constructors.Length}' constructors on type '{type.FullName}' for injection. " +
                           $"Should be only one. Used first of them.");
            }
                
            var constructor = constructors.FirstOrDefault(c => c.IsPublic && !c.IsStatic);
            if (constructor == null)
            {
                throw new Exception(
                    $"Failed to find PUBLIC NON-STATIC constructor on type '{type.FullName}' for injection." +
                    $" Prefer new() for it instead or change constructor's visibility.");
            }
            
            var parameters = constructor.GetParameters();
            if (parameters.Length == 0)
                return Activator.CreateInstance(type) as T;
            
            var resolvedParameters = constructor
                .GetParameters()
                .Select(p => resolver.Resolve(p.ParameterType))
                .ToArray();
            
            return Activator.CreateInstance(type, resolvedParameters) as T;
        }
    }
}