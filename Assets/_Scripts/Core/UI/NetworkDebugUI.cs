using Core.Network;
using Core.Network.Client;
using Core.Network.Messages;
using Mirror;
using UnityEngine;
using UnityEngine.UI;

namespace Core.UI
{
    /// <summary>
    /// Отладочный UI для тестирования сетевых функций
    /// </summary>
    public class NetworkDebugUI : MonoBehaviour
    {
        [Header("UI References")]
        public Button startServerButton;
        public Button startClientButton;
        public Button startHostButton;
        public Button stopButton;
        public Button readyButton;
        
        public Text statusText;
        public Text playersText;
        public Text pingText;
        
        [Header("Settings")]
        public string serverAddress = "localhost";
        
        private TileBattleCraftNetworkManager _networkManager;
        private NetworkClientController _clientController;
        
        private void Start()
        {
            // Находим компоненты
            _networkManager = FindObjectOfType<TileBattleCraftNetworkManager>();
            _clientController = FindObjectOfType<NetworkClientController>();
            
            if (_networkManager == null)
            {
                Debug.LogError("[NetworkDebugUI] TileBattleCraftNetworkManager not found!");
                return;
            }
            
            // Настраиваем кнопки
            SetupButtons();
            
            // Подписываемся на события
            if (_clientController != null)
            {
                _clientController.OnConnectedToServer += UpdateUI;
                _clientController.OnDisconnectedFromServer += UpdateUI;
                _clientController.OnMatchStateChanged += OnMatchStateChanged;
            }
            
            // Обновляем UI
            UpdateUI();
        }
        
        private void OnDestroy()
        {
            // Отписываемся от событий
            if (_clientController != null)
            {
                _clientController.OnConnectedToServer -= UpdateUI;
                _clientController.OnDisconnectedFromServer -= UpdateUI;
                _clientController.OnMatchStateChanged -= OnMatchStateChanged;
            }
        }
        
        private void Update()
        {
            // Обновляем UI каждый кадр
            UpdateUI();
        }
        
        private void SetupButtons()
        {
            if (startServerButton != null)
                startServerButton.onClick.AddListener(StartServer);
                
            if (startClientButton != null)
                startClientButton.onClick.AddListener(StartClient);
                
            if (startHostButton != null)
                startHostButton.onClick.AddListener(StartHost);
                
            if (stopButton != null)
                stopButton.onClick.AddListener(Stop);
                
            if (readyButton != null)
                readyButton.onClick.AddListener(ToggleReady);
        }
        
        private void UpdateUI()
        {
            if (_networkManager == null) return;
            
            // Обновляем статус
            if (statusText != null)
            {
                string status = "Disconnected";
                
                if (NetworkServer.active && NetworkClient.active)
                    status = "Host";
                else if (NetworkServer.active)
                    status = "Server";
                else if (NetworkClient.active)
                    status = NetworkClient.isConnected ? "Client (Connected)" : "Client (Connecting...)";
                    
                statusText.text = $"Status: {status}";
            }
            
            // Обновляем количество игроков
            if (playersText != null)
            {
                int playerCount = 0;
                if (NetworkServer.active)
                {
                    playerCount = _networkManager.GetSpawnedPlayers().Count;
                }
                
                playersText.text = $"Players: {playerCount}/{_networkManager.maxPlayersPerMatch}";
            }
            
            // Обновляем пинг
            if (pingText != null)
            {
                if (NetworkClient.active && NetworkClient.isConnected)
                {
                    double ping = NetworkTime.rtt * 1000;
                    pingText.text = $"Ping: {ping:F0}ms";
                }
                else
                {
                    pingText.text = "Ping: --";
                }
            }
            
            // Обновляем доступность кнопок
            UpdateButtonStates();
        }
        
        private void UpdateButtonStates()
        {
            bool isActive = NetworkServer.active || NetworkClient.active;
            
            if (startServerButton != null)
                startServerButton.interactable = !isActive;
                
            if (startClientButton != null)
                startClientButton.interactable = !isActive;
                
            if (startHostButton != null)
                startHostButton.interactable = !isActive;
                
            if (stopButton != null)
                stopButton.interactable = isActive;
                
            if (readyButton != null)
            {
                readyButton.interactable = NetworkClient.active && NetworkClient.isConnected;
                
                // Обновляем текст кнопки готовности
                Text buttonText = readyButton.GetComponentInChildren<Text>();
                if (buttonText != null && _clientController != null)
                {
                    buttonText.text = _clientController.IsReady() ? "Not Ready" : "Ready";
                }
            }
        }
        
        private void OnMatchStateChanged(MatchStateMessage.State state)
        {
            Debug.Log($"[NetworkDebugUI] Match state changed: {state}");
        }
        
        #region Button Handlers
        
        private void StartServer()
        {
            Debug.Log("[NetworkDebugUI] Starting server...");
            _networkManager.StartServer();
        }
        
        private void StartClient()
        {
            Debug.Log("[NetworkDebugUI] Starting client...");
            _networkManager.networkAddress = serverAddress;
            _networkManager.StartClient();
        }
        
        private void StartHost()
        {
            Debug.Log("[NetworkDebugUI] Starting host...");
            _networkManager.StartHost();
        }
        
        private void Stop()
        {
            Debug.Log("[NetworkDebugUI] Stopping network...");
            
            if (NetworkServer.active && NetworkClient.active)
                _networkManager.StopHost();
            else if (NetworkServer.active)
                _networkManager.StopServer();
            else if (NetworkClient.active)
                _networkManager.StopClient();
        }
        
        private void ToggleReady()
        {
            if (_clientController != null)
            {
                _clientController.SetReady(!_clientController.IsReady());
            }
        }
        
        #endregion
        
        #region GUI (Fallback)
        
        private void OnGUI()
        {
            // Fallback GUI если UI компоненты не настроены
            if (startServerButton != null) return; // Если есть UI компоненты, не показываем OnGUI
            
            GUILayout.BeginArea(new Rect(10, 10, 300, 400));
            
            GUILayout.Label($"Network Status: {GetNetworkStatus()}");
            GUILayout.Label($"Players: {GetPlayerCount()}/{_networkManager?.maxPlayersPerMatch ?? 0}");
            
            if (NetworkClient.active && NetworkClient.isConnected)
            {
                double ping = NetworkTime.rtt * 1000;
                GUILayout.Label($"Ping: {ping:F0}ms");
            }
            
            GUILayout.Space(10);
            
            bool isActive = NetworkServer.active || NetworkClient.active;
            
            GUI.enabled = !isActive;
            if (GUILayout.Button("Start Server"))
                StartServer();
                
            if (GUILayout.Button("Start Client"))
                StartClient();
                
            if (GUILayout.Button("Start Host"))
                StartHost();
            
            GUI.enabled = isActive;
            if (GUILayout.Button("Stop"))
                Stop();
            
            GUI.enabled = NetworkClient.active && NetworkClient.isConnected;
            string readyText = _clientController?.IsReady() == true ? "Not Ready" : "Ready";
            if (GUILayout.Button(readyText))
                ToggleReady();
            
            GUI.enabled = true;
            
            GUILayout.EndArea();
        }
        
        private string GetNetworkStatus()
        {
            if (NetworkServer.active && NetworkClient.active)
                return "Host";
            else if (NetworkServer.active)
                return "Server";
            else if (NetworkClient.active)
                return NetworkClient.isConnected ? "Client (Connected)" : "Client (Connecting...)";
            else
                return "Disconnected";
        }
        
        private int GetPlayerCount()
        {
            if (NetworkServer.active && _networkManager != null)
                return _networkManager.GetSpawnedPlayers().Count;
            return 0;
        }
        
        #endregion
    }
}
