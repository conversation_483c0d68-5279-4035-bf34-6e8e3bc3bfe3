using System;
using System.Runtime.CompilerServices;

namespace Core.Logger
{
    public class Log
    {
        private const string LogFormat = "{0}: {1}";
        private readonly string _service;

        public Log(string service)
        {
            _service = service;
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Error(string error)
        {
            UnityEngine.Debug.LogErrorFormat(LogFormat, _service, error);
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Exception(Exception exception)
        {
            UnityEngine.Debug.LogErrorFormat(LogFormat, _service, exception.Message);
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Warning(string warning)
        {
            UnityEngine.Debug.LogWarningFormat(LogFormat, _service, warning);
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public void Info(string info)
        {
            UnityEngine.Debug.LogFormat(LogFormat, _service, info);
        }
    }
}