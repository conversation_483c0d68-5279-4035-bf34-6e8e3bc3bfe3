using System;
using System.Collections.Generic;
using System.Runtime.CompilerServices;
using Core.Disposable;
using Extensions;

namespace Core.Rx
{
    public interface IRx<out T>
    {
        T Value { get; }
        
        IDisposable Listen(Action<T> callback);
        IDisposable Listen(Action onChange);
    }
    
    public class Rx<T> : IRx<T>
    {
        private readonly IList<Action<T>> _callbacks = new List<Action<T>>();
        private T _value;

        public Rx(T @default = default)
        {
            Value = @default;
        }

        public T Value
        {
            get => _value;
            set
            {
                _value = value;
                InvokeCallbacks();
            }
        }

        public IDisposable Listen(Action<T> callback)
        {
            _callbacks.Add(callback);
            return new DeferredAction().Add(() => _callbacks.Remove(callback));
        }

        public IDisposable Listen(Action callback)
        {
            Action<T> newCallback = _ => callback();
            _callbacks.Add(newCallback);
            return new DeferredAction().Add(() => _callbacks.Remove(newCallback));
        }

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        private void InvokeCallbacks()
        {
            if (_callbacks.IsNullOrEmpty())
                return;

            foreach (var callback in _callbacks)
            {
                if (callback == null)
                    continue;
                
                callback(Value);
            }
        }
    }
    
    public interface IRx
    {
        IDisposable Listen(Action callback);
    }
    
    public class Rx : IRx
    {
        private readonly IList<Action> _callbacks = new List<Action>();

        public void Call()
        {
            if (_callbacks.IsNullOrEmpty())
                return;

            var callbacks = new List<Action>(_callbacks);
            foreach (var callback in callbacks)
            {
                if (callback == null)
                    continue;
                
                callback();
            }
        }
        
        public IDisposable Listen(Action callback)
        {
            _callbacks.Add(callback);
            return new DeferredAction().Add(() => _callbacks.Remove(callback));
        }
    }
}