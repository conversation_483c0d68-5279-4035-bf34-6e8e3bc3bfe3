using System;

namespace Core.Rx
{
    public static class RxExtensions
    {
        public static IDisposable Listen<T>(this IRx<T> rx, Action<T> onChange)
        {
            onChange(rx.Value);
            return rx.Listen(onChange);
        }
        
        public static IDisposable Listen<T>(this IRx<T> rx, Action onChange)
        {
            onChange();
            return rx.Listen(onChange);
        }
    }
}