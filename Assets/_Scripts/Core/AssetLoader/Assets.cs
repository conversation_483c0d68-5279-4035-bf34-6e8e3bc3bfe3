using System;
using UnityEngine;

namespace Core.AssetLoader
{
    public class Assets
    {
        private const string PathToPrefabs = "Prefabs";
        private const string PathToSprite = "Sprites";
        private const string PathToFallbackSprite = "Sprites/Fallback";
        
        public static Assets Instance { get; } = new();

        private Assets()
        { }

        public Sprite Sprite(string path)
        {
            var sprite = Resources.Load<Sprite>($"{PathToSprite}/{path}");
            if (!sprite)
                return Resources.Load<Sprite>(PathToFallbackSprite);
            return sprite;
        }

        public T Prefab<T>(string path)
            where T : Component
        {
            var prefab = Resources.Load<T>($"{PathToPrefabs}/{path}");
            if (!prefab)
                throw new NullReferenceException($"Can't find prefab on '{PathToPrefabs}/{path}'");
            return prefab;
        }
    }
}