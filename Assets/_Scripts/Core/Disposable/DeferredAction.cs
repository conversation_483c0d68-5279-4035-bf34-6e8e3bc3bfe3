using System;
using System.Collections.Generic;
using Core.Logger;
using Extensions;

namespace Core.Disposable
{
    public class DeferredAction : IDisposable
    {
        public static DeferredAction Empty { get; } = new ();
        
        private static readonly Log _logger = new (nameof(DeferredAction));
        private readonly IList<Action> _callbacks = new List<Action>();

        private bool _disposed;

        public DeferredAction Add(Action action)
        {
            _callbacks.Add(action);
            return this;
        }
        
        public void Dispose()
        {
            if (_disposed)
            {
                _logger.Error("Double dispose detected");
                return;
            }
            
            if (_callbacks.IsNullOrEmpty())
                return;

            foreach (var action in _callbacks)
            {
                if (action == null)
                    return;

                action();
            }

            _disposed = true;
        }
    }
}