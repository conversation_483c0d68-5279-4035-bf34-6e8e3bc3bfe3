using Core.EntryPoints.Menu;
using Core.Scenes;
using Cysharp.Threading.Tasks;
using UnityEngine.Scripting;

namespace Core.Match.Disconnection
{
    [Preserve]
    public class MatchDisconnector : IMatchDisconnector
    {
        private readonly ISceneNavigator _sceneNavigator;

        [Preserve]
        public MatchDisconnector(
            ISceneNavigator sceneNavigator
        )
        {
            _sceneNavigator = sceneNavigator;
        }
        
        public void Disconnect(string reason)
        {
            _sceneNavigator.ChangeAsync(SceneType.Menu, new MenuSceneParams(reason)).Forget();
        }
    }
}