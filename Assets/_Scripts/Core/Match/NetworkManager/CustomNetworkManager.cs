using System.Collections.Generic;
using Core.Extensions;
using Core.Logger;
using Core.Match.Player;
using Core.Rx;
using Mirror;
using Unity.VisualScripting;
using UnityEngine;
using Zenject;

namespace Core.Match.NetworkManager
{
    public class CustomNetworkManager : Mirror.NetworkManager
    {
        public const int MaxPlayerCount = 2;

        private DiContainer _scope = null!;
        
        public static CustomNetworkManager Instance { get; private set; }
        
        private static readonly Log _log = new Log(nameof(CustomNetworkManager));

        private static readonly Rx<List<NetPlayer>> _players = new Rx<List<NetPlayer>>(new List<NetPlayer>(2));
        public IRx<IReadOnlyList<NetPlayer>> Players => _players;

        [Inject]
        private void In(DiContainer scope)
        {
            _scope = scope;
        }

        public override void Awake()
        {
            Instance = this;
            
            base.Awake();
        }

        public override void OnStartHost()
        {
            base.OnStartHost();

            CreatePlayer("player-bot");
        }

        public override void OnServerReady(NetworkConnectionToClient conn)
        {
            base.OnServerReady(conn);

            if (_players.Value.Count >= MaxPlayerCount)
            {
                _log.Error($"Max players reached ({MaxPlayerCount}), disconnecting client {conn.connectionId}");
                conn.Disconnect();
                return;
            }
            
            OnServerAddPlayer(conn);
        }

        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            var player = CreatePlayer("player");
            NetworkServer.AddPlayerForConnection(conn, player);
        }

        private GameObject CreatePlayer(string gameObjectName)
        {
            var player = _scope.InstantiateComponentOnNewGameObject<NetPlayer>();
            var identity = player.AddComponent<NetworkIdentity>();
            NetworkServer.Spawn(player.gameObject);
            NetworkServer.aoi.OnSpawned(identity);

            var players = new List<NetPlayer>(_players.Value) { player.GetComponent<NetPlayer>() };
            _players.Value = players;
            return player.gameObject;
        }
    }
}