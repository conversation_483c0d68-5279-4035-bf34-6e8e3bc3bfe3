using Core.Rx;
using Mirror;

namespace Core.Match.Player.Attaches.Score
{
    public class NetScorePlayerAttach : NetPlayerAttach
    {
        [SyncVar(hook = nameof(SyncScore))]
        private byte _syncScore;
        
        private readonly Rx<byte> _score = new ();
        public IRx<byte> Score => _score;

        #region Client

        [Client]
        private void SyncScore(byte _, byte score)
        {
            _score.Value = score;
        }

        #endregion
        
        #region Server

        protected override void ServerEntry()
        {
            base.ServerEntry();
            SetScore(0);
        }

        [Server]
        public void Increase()
        {
            SetScore((byte)(_syncScore + 1));
        }

        [Server]
        private void SetScore(byte score)
        {
            _score.Value = score;
            _syncScore = score;
        }

        #endregion
    }
}