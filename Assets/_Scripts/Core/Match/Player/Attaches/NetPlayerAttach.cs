using Mirror;

namespace Core.Match.Player.Attaches
{
    public abstract class NetPlayerAttach : NetworkBehaviour
    {
        protected NetPlayer Player { get; private set; }
        
        #region Server
        protected ServerNetPlayerAttachContext ServerContext { [Server] get; [Server] private set; } = null!;

        public sealed override void OnStartServer()
        {
            base.OnStartServer();
        }

        public sealed override void OnStopServer()
        {
            base.OnStopServer();
            ServerExit();
        }

        [Server]
        public void ServerReadyUp(NetPlayer owner, ServerNetPlayerAttachContext context)
        {
            Player = owner;
            ServerContext = context;
            
            ServerReady();
        }

        [Server]
        protected virtual void ServerReady() {}

        [Server]
        protected virtual void ServerExit() {}

        #endregion

        #region Client
        
        protected ClientNetPlayerAttachContext ClientContext { [Client] get; [Client] private set; } = null!;
        
        public sealed override void OnStartClient()
        {
            base.OnStartClient();
        }

        public sealed override void OnStopClient()
        {
            base.OnStopClient();
            ClientExit();
        }

        [Client]
        public void ClientReadyUp(NetPlayer owner, ClientNetPlayerAttachContext context)
        {
            Player = owner;
            ClientContext = context;
            ClientReady();
        }

        [Client]
        public virtual void ClientReady() {}

        [Client]
        protected virtual void ClientExit() {}

        #endregion
    }
}