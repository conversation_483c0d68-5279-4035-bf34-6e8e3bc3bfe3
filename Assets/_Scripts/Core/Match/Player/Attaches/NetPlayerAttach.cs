using Mirror;

namespace Core.Match.Player.Attaches
{
    public abstract class NetPlayerAttach : NetworkBehaviour
    {
        #region Shared

        protected NetPlayerAttachContext Context { get; private set; } = null!;

        #endregion
        
        #region Server

        public sealed override void OnStartServer()
        {
            base.OnStartServer();
            ServerEntry();
        }

        public sealed override void OnStopServer()
        {
            base.OnStopServer();
            ServerExit();
        }
        
        [Server]
        protected virtual void ServerEntry() {}
                
        [Server]
        protected virtual void ServerExit() {}

        #endregion

        #region Client

        public sealed override void OnStartClient()
        {
            base.OnStartClient();
            ClientEntry();
        }

        public sealed override void OnStopClient()
        {
            base.OnStopClient();
            ClientExit();
        }
        
        [Client]
        protected virtual void ClientEntry() {}

        [Client]
        protected virtual void ClientExit() {}

        #endregion
    }
}