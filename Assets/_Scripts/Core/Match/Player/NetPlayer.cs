using Core.Match.Player.Attaches;
using Core.Match.Player.Attaches.Score;
using Mirror;
using Unity.VisualScripting;

namespace Core.Match.Player
{
    public class NetPlayer : NetworkBehaviour
    {
        public NetScorePlayerAttach Score { get; private set; } = null!;
        
        public override void OnStartServer()
        {
            base.OnStartServer();
            
            Score = Attach<NetScorePlayerAttach>();
        }

        private T Attach<T>() where T : NetPlayerAttach
        {
            return gameObject.GetOrAddComponent<T>();
        }
    }
}