using System.Collections.Generic;
using Core.Match.Contexts;
using Core.Match.Player.Attaches;
using Core.Match.Player.Attaches.Match;
using Core.Match.Player.Attaches.Score;
using Mirror;
using Unity.VisualScripting;
using Zenject;

namespace Core.Match.Player
{
    public class NetPlayer : NetworkBehaviour
    {
        public NetworkMatch Match { get; private set; } = null!;
        public NetScorePlayerAttach Score { get; private set; } = null!;
        public NetMatchDataPlayerAttach MatchData { get; private set; } = null!;

        private readonly IList<NetPlayerAttach> _attaches = new List<NetPlayerAttach>();

        #region Server

        private IServerMatchContextController _serverMatchContextController;
        
        [Inject]
        private void ServerIn(IServerMatchContextController serverMatchContextController)
        {
            _serverMatchContextController = serverMatchContextController;
        }
        
        public sealed override void OnStartServer()
        {
            base.OnStartServer();

            Match = gameObject.AddComponent<NetworkMatch>();
            Score = Attach<NetScorePlayerAttach>();
            MatchData = Attach<NetMatchDataPlayerAttach>();
        }

        [Server]
        public void ReadyUp()
        {
            var context = new ServerNetPlayerAttachContext(_serverMatchContextController.Get(Match.matchId));
            foreach (var attach in _attaches)
                attach.ServerReadyUp(this, context);
        }

        private T Attach<T>() where T : NetPlayerAttach
        {
            var attach = gameObject.GetOrAddComponent<T>();
            _attaches.Add(attach);
            return attach;
        }

        #endregion

        #region Client
        
        private IClientMatchContextController _clientMatchContextController = null!;
        
        [Inject]
        private void ClientIn(IClientMatchContextController clientMatchContextController)
        {
            _clientMatchContextController = clientMatchContextController;
        }

        [Client]
        public void Client_ReadyUp()
        {
            var context = new ClientNetPlayerAttachContext(_clientMatchContextController.Context);
            foreach (var attach in _attaches)
                attach.ClientReadyUp(this, context);
        }

        #endregion
    }
}