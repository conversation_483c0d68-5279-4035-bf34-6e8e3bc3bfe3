using Core.EntryPoints.Match;
using Core.Scenes;
using UnityEngine.Scripting;

namespace Core.Match.Presenter
{
    [Preserve]
    public class MatchPresenter : IMatchPresenter
    {
        private readonly ISceneNavigator _sceneNavigator;

        [Preserve]
        public MatchPresenter(
            ISceneNavigator sceneNavigator)
        {
            _sceneNavigator = sceneNavigator;
        }
        
        public void ShowLocalhostAndBots()
        {
            _sceneNavigator.ChangeAsync(SceneType.Match, new MatchSceneParams(MatchType.Localhost));
        }
    }
}