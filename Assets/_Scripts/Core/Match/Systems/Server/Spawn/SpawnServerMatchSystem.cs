using Core.Network;
using Mirror;
using UnityEngine;

namespace Core.Match.Systems.Server.Spawn
{
    /// <summary>
    /// Система спавна игроков на сервере
    /// Отвечает за создание и настройку NetPlayer объектов
    /// </summary>
    public class SpawnServerMatchSystem : ServerMatchSystem
    {
        private TileBattleCraftNetworkManager _networkManager;

        protected override void EntryInternal()
        {
            Debug.Log("[SpawnServerMatchSystem] Initializing spawn system");

            // Получаем NetworkManager
            _networkManager = TileBattleCraftNetworkManager.singleton as TileBattleCraftNetworkManager;
            if (_networkManager == null)
            {
                Debug.LogError("[SpawnServerMatchSystem] TileBattleCraftNetworkManager not found!");
                return;
            }

            // Подписываемся на события создания игроков
            _networkManager.OnPlayerSpawned += OnPlayerSpawned;
            _networkManager.OnPlayerDespawned += OnPlayerDespawned;

            // Обновляем контекст матча с уже существующими игроками
            UpdateMatchContext();

            Debug.Log("[SpawnServerMatchSystem] Spawn system initialized");
        }

        protected override void ExitInternal()
        {
            Debug.Log("[SpawnServerMatchSystem] Shutting down spawn system");

            // Отписываемся от событий
            if (_networkManager != null)
            {
                _networkManager.OnPlayerSpawned -= OnPlayerSpawned;
                _networkManager.OnPlayerDespawned -= OnPlayerDespawned;
            }

            Debug.Log("[SpawnServerMatchSystem] Spawn system shut down");
        }

        /// <summary>
        /// Обработчик создания нового игрока
        /// </summary>
        private void OnPlayerSpawned(Core.Match.Player.NetPlayer netPlayer)
        {
            Debug.Log($"[SpawnServerMatchSystem] Player spawned: {netPlayer.name}");
            UpdateMatchContext();
        }

        /// <summary>
        /// Обработчик удаления игрока
        /// </summary>
        private void OnPlayerDespawned(Core.Match.Player.NetPlayer netPlayer)
        {
            Debug.Log($"[SpawnServerMatchSystem] Player despawned: {netPlayer.name}");
            UpdateMatchContext();
        }

        /// <summary>
        /// Обновляет контекст матча с текущими игроками
        /// </summary>
        private void UpdateMatchContext()
        {
            if (_networkManager == null || Context?.MatchContext == null)
                return;

            var players = _networkManager.GetSpawnedPlayers();

            // Устанавливаем игроков в контекст
            if (players.Count > 0)
            {
                Context.MatchContext.LocalPlayer = players[0];
                Debug.Log($"[SpawnServerMatchSystem] LocalPlayer set: {players[0].name}");
            }

            if (players.Count > 1)
            {
                Context.MatchContext.OpponentPlayer = players[1];
                Debug.Log($"[SpawnServerMatchSystem] OpponentPlayer set: {players[1].name}");
            }

            Debug.Log($"[SpawnServerMatchSystem] Match context updated. Players: {players.Count}");
        }
    }
}