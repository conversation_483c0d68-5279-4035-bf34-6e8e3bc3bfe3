using Core.Network;
using Core.Network.Messages;
using Mirror;
using UnityEngine;

namespace Core.Match.Systems.Server.MatchState
{
    /// <summary>
    /// Система управления состоянием матча на сервере
    /// Отслеживает готовность игроков и управляет переходами состояний
    /// </summary>
    public class MatchStateServerSystem : ServerMatchSystem
    {
        public enum MatchState
        {
            WaitingForPlayers,  // Ожидание игроков
            PlayersReady,       // Игроки готовы
            InProgress,         // Матч в процессе
            Finished            // Матч завершен
        }
        
        private TileBattleCraftNetworkManager _networkManager;
        private MatchState _currentState = MatchState.WaitingForPlayers;
        
        public MatchState CurrentState => _currentState;
        
        // События состояния матча
        public System.Action<MatchState> OnStateChanged;
        public System.Action OnMatchReady;
        public System.Action OnMatchStarted;
        public System.Action OnMatchFinished;
        
        protected override void EntryInternal()
        {
            Debug.Log("[MatchStateServerSystem] Initializing match state system");
            
            // Получаем NetworkManager
            _networkManager = TileBattleCraftNetworkManager.singleton as TileBattleCraftNetworkManager;
            if (_networkManager == null)
            {
                Debug.LogError("[MatchStateServerSystem] TileBattleCraftNetworkManager not found!");
                return;
            }
            
            // Подписываемся на события игроков
            _networkManager.OnPlayerSpawned += OnPlayerJoined;
            _networkManager.OnPlayerDespawned += OnPlayerLeft;
            
            // Проверяем текущее состояние
            CheckMatchState();
            
            Debug.Log("[MatchStateServerSystem] Match state system initialized");
        }
        
        protected override void ExitInternal()
        {
            Debug.Log("[MatchStateServerSystem] Shutting down match state system");
            
            // Отписываемся от событий
            if (_networkManager != null)
            {
                _networkManager.OnPlayerSpawned -= OnPlayerJoined;
                _networkManager.OnPlayerDespawned -= OnPlayerLeft;
            }
            
            Debug.Log("[MatchStateServerSystem] Match state system shut down");
        }
        
        /// <summary>
        /// Обработчик присоединения игрока
        /// </summary>
        private void OnPlayerJoined(Core.Match.Player.NetPlayer player)
        {
            Debug.Log($"[MatchStateServerSystem] Player joined: {player.name}");
            CheckMatchState();
        }
        
        /// <summary>
        /// Обработчик выхода игрока
        /// </summary>
        private void OnPlayerLeft(Core.Match.Player.NetPlayer player)
        {
            Debug.Log($"[MatchStateServerSystem] Player left: {player.name}");
            CheckMatchState();
        }
        
        /// <summary>
        /// Проверяет и обновляет состояние матча
        /// </summary>
        private void CheckMatchState()
        {
            if (_networkManager == null)
                return;
                
            var players = _networkManager.GetSpawnedPlayers();
            int playerCount = players.Count;
            
            MatchState newState = _currentState;
            
            switch (_currentState)
            {
                case MatchState.WaitingForPlayers:
                    if (playerCount >= 2)
                    {
                        newState = MatchState.PlayersReady;
                    }
                    break;
                    
                case MatchState.PlayersReady:
                    if (playerCount < 2)
                    {
                        newState = MatchState.WaitingForPlayers;
                    }
                    else if (AreAllPlayersReady())
                    {
                        newState = MatchState.InProgress;
                    }
                    break;
                    
                case MatchState.InProgress:
                    if (playerCount < 2)
                    {
                        newState = MatchState.WaitingForPlayers;
                    }
                    break;
                    
                case MatchState.Finished:
                    if (playerCount < 2)
                    {
                        newState = MatchState.WaitingForPlayers;
                    }
                    break;
            }
            
            if (newState != _currentState)
            {
                ChangeState(newState);
            }
        }
        
        /// <summary>
        /// Проверяет готовность всех игроков
        /// </summary>
        private bool AreAllPlayersReady()
        {
            // Пока что считаем, что игроки готовы сразу после подключения
            // В будущем можно добавить систему готовности
            return true;
        }
        
        /// <summary>
        /// Изменяет состояние матча
        /// </summary>
        private void ChangeState(MatchState newState)
        {
            MatchState oldState = _currentState;
            _currentState = newState;
            
            Debug.Log($"[MatchStateServerSystem] State changed: {oldState} -> {newState}");
            
            // Уведомляем о смене состояния
            OnStateChanged?.Invoke(newState);

            // Отправляем сообщение клиентам
            BroadcastStateChange(newState);

            // Специфичные события
            switch (newState)
            {
                case MatchState.PlayersReady:
                    OnMatchReady?.Invoke();
                    break;

                case MatchState.InProgress:
                    OnMatchStarted?.Invoke();
                    _networkManager?.BroadcastMatchStart();
                    break;

                case MatchState.Finished:
                    OnMatchFinished?.Invoke();
                    break;
            }
        }
        
        /// <summary>
        /// Принудительно запускает матч (если игроки готовы)
        /// </summary>
        public void StartMatch()
        {
            if (_currentState == MatchState.PlayersReady)
            {
                ChangeState(MatchState.InProgress);
            }
            else
            {
                Debug.LogWarning("[MatchStateServerSystem] Cannot start match - players not ready");
            }
        }
        
        /// <summary>
        /// Завершает матч
        /// </summary>
        public void FinishMatch()
        {
            if (_currentState == MatchState.InProgress)
            {
                ChangeState(MatchState.Finished);
            }
        }

        /// <summary>
        /// Отправляет изменение состояния всем клиентам
        /// </summary>
        private void BroadcastStateChange(MatchState state)
        {
            if (_networkManager == null) return;

            MatchStateMessage.State messageState = ConvertToMessageState(state);
            _networkManager.BroadcastMatchState(messageState);
        }

        /// <summary>
        /// Конвертирует серверное состояние в состояние сообщения
        /// </summary>
        private MatchStateMessage.State ConvertToMessageState(MatchState state)
        {
            switch (state)
            {
                case MatchState.WaitingForPlayers:
                    return MatchStateMessage.State.WaitingForPlayers;
                case MatchState.PlayersReady:
                    return MatchStateMessage.State.PlayersReady;
                case MatchState.InProgress:
                    return MatchStateMessage.State.InProgress;
                case MatchState.Finished:
                    return MatchStateMessage.State.Finished;
                default:
                    return MatchStateMessage.State.WaitingForPlayers;
            }
        }
    }
}
