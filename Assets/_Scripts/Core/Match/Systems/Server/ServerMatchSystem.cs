namespace Core.Match.Systems
{
    public abstract class ServerMatchSystem
    {
        protected ServerMatchSystemContext Context { get; private set; } = null!;
        
        public void Init(
            ServerMatchSystemContext context)
        {
            Context = context;
        }
        
        public void Entry()
        {
            EntryInternal();
        }

        public void Exit()
        {
            ExitInternal();
        }

        protected abstract void EntryInternal();
        protected abstract void ExitInternal();
    }
}