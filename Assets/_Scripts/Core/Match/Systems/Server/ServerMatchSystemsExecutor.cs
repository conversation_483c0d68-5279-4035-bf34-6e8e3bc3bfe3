using System.Collections.Generic;
using Extensions;

namespace Core.Match.Systems
{
    public class ServerSystemsExecutor
    {
        private readonly ServerMatchSystemContext _context;
        
        private readonly IList<ServerMatchSystem> _systems = new List<ServerMatchSystem>();

        public ServerSystemsExecutor(
            ServerMatchSystemContext context)
        {
            _context = context;
        }
        
        public T Reg<T>() where T : ServerMatchSystem, new()
        {
            var system = new T();
            system.Init(_context);
            _systems.Add(system);
            return system;
        }

        public void ExecuteAll()
        {
            if (_systems.IsNullOrEmpty())
                return;
            
            foreach (var system in _systems)
            {
                system.Entry();
            }
        }

        public void StopAll()
        {
            if (_systems.IsNullOrEmpty())
                return;
            
            foreach (var system in _systems)
            {
                system.Exit();
            }
        }
    }
}