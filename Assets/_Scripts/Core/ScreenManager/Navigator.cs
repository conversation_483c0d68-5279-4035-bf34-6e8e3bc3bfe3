using System;
using System.Collections.Generic;
using System.Text;
using Core.AssetLoader;
using Core.Extensions;
using Core.ScreenManagement.Layers;
using Cysharp.Threading.Tasks;
using Extensions;
using VContainer;
using Object = UnityEngine.Object;

namespace Core.ScreenManagement
{
    [UnityEngine.Scripting.Preserve]
    public class Navigator : INavigator
    {
        private const string PathToScreens = "Screens";
        
        private readonly IScreenLayersManager _screenLayersManager;
        private readonly IObjectResolver _objectResolver;

        private readonly IDictionary<Type, ScreenController> _loadedScreensControllersByType = new Dictionary<Type, ScreenController>();
        private readonly Stack<ScreenController> _activeScreenControllersStack = new Stack<ScreenController>();
        
        [UnityEngine.Scripting.Preserve]
        public Navigator(
            IScreenLayersManager screenLayersManager,
            IObjectResolver objectResolver)
        {
            _screenLayersManager = screenLayersManager;
            _objectResolver = objectResolver;
        }

        public void Show<TScreen>(object parameters = null)
            where TScreen : ScreenMono
        {
            if (!_loadedScreensControllersByType.TryGetValue(typeof(TScreen), out var screenController))
            {
                screenController = CreateScreen<TScreen>();
                _loadedScreensControllersByType.Add(typeof(TScreen), screenController);
            }

            if (screenController.ShowMode == ScreenShowMode.Exchange &&
                _activeScreenControllersStack.TryPeek(out var previousController))
            {
                previousController.Hide();
            }

            _activeScreenControllersStack.Push(screenController);
            screenController.AddParameters(parameters);
            screenController.Show();
        }

        public UniTask ShowAndWaitExitAsync<TScreen>(object parameters = null)
            where TScreen : ScreenMono
        {
            Show<TScreen>(parameters);
            return _activeScreenControllersStack.Peek().WaitForExitAsync();
        }

        private void PopStack()
        {
            var closedScreenController = _activeScreenControllersStack.Pop();
            
            if (closedScreenController.ShowMode != ScreenShowMode.Exchange)
                return;
            
            if (!_activeScreenControllersStack.TryPeek(out var previousController))
                return;
            
            previousController.Show();
        }

        private ScreenController CreateScreen<TScreen>()
            where TScreen : ScreenMono
        {
            var pathToScreen = GetScreenPath<TScreen>();
            var prefab = Assets.Instance.Prefab<TScreen>(pathToScreen);
            var view = Object.Instantiate(prefab);
            if (view.IsNullOrDestroyed())
                throw new NullReferenceException($"Failed to load screen on path '{pathToScreen}'");

            var controller = _objectResolver.CreateAndInject<ScreenController>(view.ControllerType);
            if (controller == null)
                throw new ArgumentException($"Controller '{view.ControllerType.Name}' isn't inherited from {nameof(ScreenController)}");

            var parent = _screenLayersManager.Get(controller.LayerType);
            view!.transform.SetParent(parent.transform, false);
            
            controller.OnCreated(view, PopStack);
            return controller;
        }

        private static string GetScreenPath<TScreen>() where TScreen : ScreenMono
        {
            var screenName = typeof(TScreen).Name.Replace("Screen", "");

            var sb = new StringBuilder();
            sb.Append(PathToScreens);
            sb.Append("/");
            sb.Append(screenName);
            sb.Append("/");
            sb.Append(screenName);
            return sb.ToString();
        }
    }
}