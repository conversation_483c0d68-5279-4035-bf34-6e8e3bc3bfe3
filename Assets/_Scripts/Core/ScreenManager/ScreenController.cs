using System;
using Core.Disposable;
using Core.ScreenManagement.Layers;
using Cysharp.Threading.Tasks;
using Extensions;
using UnityEngine.Scripting;

namespace Core.ScreenManagement
{
    [Preserve]
    public abstract class ScreenController
    {
        public virtual ScreenLayerType LayerType => ScreenLayerType.Main;
        public virtual ScreenShowMode ShowMode => ScreenShowMode.Exchange;
        
        private Action _removeFromStack;
        private DeferredAction _disposeOnHide;
        private UniTaskCompletionSource<object> _exitCompletionSource;

        public virtual ScreenMono BaseView { get; private set; }

        [Preserve]
        protected ScreenController()
        {
        }

        public virtual void AddParameters(object parameters)
        { }

        public void OnCreated(ScreenMono view, Action removeFromStack)
        {
            BaseView = view;
            _removeFromStack = removeFromStack;
            OnCreatedInternal();
        }

        public void OnDestroy()
        {
            OnDestroyInternal();
        }

        public virtual void Show()
        {
            _disposeOnHide = new DeferredAction();
            ShowInternal();
        }

        public virtual void Hide()
        {
            _disposeOnHide?.Dispose();
            HideInternal();
        }

        public UniTask<object> WaitForExitAsync()
        {
            _exitCompletionSource = new UniTaskCompletionSource<object>();
            return _exitCompletionSource.Task;
        }

        protected void Exit(object exitParameters = null)
        {
            Hide();
            _exitCompletionSource?.TrySetResult(exitParameters);
            _removeFromStack();
        }

        protected void DisOnHide(IDisposable disposable)
        {
            _disposeOnHide.Add(disposable.Dispose);
        }

        protected abstract void ShowInternal();
        protected abstract void HideInternal();
        protected virtual void OnCreatedInternal() {}
        protected virtual void OnDestroyInternal() {}
    }

    public abstract class ScreenController<TScreen> : ScreenController
        where TScreen : ScreenMono
    {
        private TScreen _view;
        protected TScreen View => _view ??= (TScreen)BaseView;

        public sealed override void Show()
        {
            base.Show();
            
            if (View.IsNullOrDestroyed())
                return;
            
            View.gameObject.SetActive(true);
            
            if (View.ExitButton)
                DisOnHide(View.ExitButton.AddOnClick(() => Exit()));
        }

        public sealed override void Hide()
        {
            base.Hide();
            
            if (View.IsNullOrDestroyed())
                return;
            
            View.gameObject.SetActive(false);
        }
    }

    public abstract class ScreenController<TScreen, TParameters> : ScreenController<TScreen>
        where TScreen : ScreenMono
    {
        protected TParameters Parameters { get; private set; }

        public sealed override void AddParameters(object parameters)
        {
            base.AddParameters(parameters);
            
            if (parameters == null)
                return;
            
            Parameters = (TParameters)parameters;
        }
    }
}