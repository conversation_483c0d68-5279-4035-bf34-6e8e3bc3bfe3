using System;
using Ui.Core.Button;
using UnityEngine;

namespace Core.ScreenManagement
{
    public abstract class ScreenMono : MonoBehaviour
    {
        [field: SerializeField] 
        public ButtonMono ExitButton { get; private set; } = null!;
        
        public abstract Type ControllerType { get; }
        
        public void Show()
        {
            gameObject.SetActive(true);
        }

        public void Hide()
        {
            gameObject.SetActive(false);
        }
    }
}