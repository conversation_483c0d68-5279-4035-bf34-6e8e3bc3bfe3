using System;
using System.Collections.Generic;
using Core.AssetLoader;
using Core.Scenes;
using Extensions;
using UnityEngine;
using VContainer;
using CollectionExtensions = Extensions.CollectionExtensions;
using Object = UnityEngine.Object;

namespace Core.ScreenManagement.Layers
{
    public class ScreenLayersManager : IScreenLayersManager
    {
        private readonly Canvas _canvasPrefab;
        
        [Inject]
        public ScreenLayersManager(ISceneNavigator sceneNavigator)
        {
            _canvasPrefab = Assets.Instance.Prefab<Canvas>("default-canvas");
        }

        private readonly IDictionary<ScreenLayerType, Canvas> _layers = new Dictionary<ScreenLayerType, Canvas>();

        public Canvas Get(ScreenLayerType type)
        {
            if (_layers.TryGetValue(type, out var canvas) && !canvas.IsNullOrDestroyed())
                return canvas;
            
            return _layers[type] = CreateCanvas(type.ToString());
        }

        private Canvas CreateCanvas(string name)
        {
            var canvas = Object.Instantiate(_canvasPrefab);
            canvas.gameObject.name = name;
            return canvas;
        }
    }
}