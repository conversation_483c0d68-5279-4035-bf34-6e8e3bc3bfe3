using System;
using UnityEngine;

namespace Extensions
{
    public static class MonoExtensions
    {
        public static bool IsNullOrDestroyed<T>(this T component)
            where T : Component
        {
            if (!component)
                return true;

            return component.gameObject.IsNullOrDestroyed();
        }
        
        public static bool IsNullOrDestroyed(this GameObject gameObject)
        {
            if (!gameObject)
                return true;
            
            try
            {
                _ = gameObject.transform.position;
            }
            catch (NullReferenceException)
            {
                return true;
            }

            return false;
        }
    }
}