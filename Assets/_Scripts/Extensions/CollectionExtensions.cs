using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.CompilerServices;

namespace Extensions
{
    public static class CollectionExtensions
    {
        public static IReadOnlyList<T> GetEnumValues<T>()
        {
            var valuesArray = Enum.GetValues(typeof(T));
            return valuesArray.Cast<T>().ToList();
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool IsNullOrEmpty<T>(this Stack<T> stack)
        {
            return stack == null || stack.Count == 0;
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool IsNullOrEmpty<T>(this IList<T> list)
        {
            return list == null || list.Count == 0;
        }
        
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool IsNullOrEmpty<K, V>(this IDictionary<K, V> dict)
        {
            return dict == null || dict.Count == 0;
        }
    }
}