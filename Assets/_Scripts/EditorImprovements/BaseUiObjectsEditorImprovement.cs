using UnityEditor;
using UnityEngine;

namespace EditorImprovements
{
    public static class BaseUiObjectsEditorImprovement
    {
        private const string PathToPrefabs = "Prefabs/base-ui";
        
        private static void Create(string path)
        {
            var prefab = Resources.Load<GameObject>($"{PathToPrefabs}/{path}");
            if (!prefab)
                return;
            
            var selectedObject = Selection.activeGameObject;
            var parent = !selectedObject ? null : selectedObject.transform;
            PrefabUtility.InstantiatePrefab(prefab, parent);
            Undo.RegisterCreatedObjectUndo(prefab, "Create " + prefab.name);
        }
        
        [MenuItem("GameObject/_Own/Text", priority = 0)]
        public static void CreateText()
        {
            Create("Text");
        }
        
        [MenuItem("GameObject/_Own/Button", priority = 0)]
        public static void CreateButton()
        {
            Create("Button");
        }
    }
}