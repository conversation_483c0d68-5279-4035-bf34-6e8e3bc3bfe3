using System;
using _MetaApi.Parts.Progress;
using Core.Rx;
using Ui.Core.Text;
using UnityEngine;

namespace _Scripts.Ui.Core.ResourcesContainer
{
    public class ResourcesMono : MonoBehaviour
    {
        [SerializeField]
        private TextMono _money = null!;

        [SerializeField] 
        private TextMono _gems = null!;
        
        private IDisposable _moneySubscription = null!;
        private IDisposable _gemsSubscription = null!;
        
        private IUserProgressMetaApiPart _progressMetaApiPart;
        
        public void Init(IUserProgressMetaApiPart progressMetaApiPart)
        {
            _progressMetaApiPart = progressMetaApiPart;
        }

        public void Show()
        {
            _moneySubscription = RxExtensions.Listen(_progressMetaApiPart.Money, OnMoneyChange);
            _gemsSubscription = RxExtensions.Listen(_progressMetaApiPart.Gems, OnGemsChange);
        }

        public void Hide()
        {
            _moneySubscription?.Dispose();
            _gemsSubscription?.Dispose();
        }

        private void OnMoneyChange(int count)
        {
            _money.Text = count.ToString();
        }

        private void OnGemsChange(int count)
        {
            _gems.Text = count.ToString();
        }
    }
}