using System;
using System.Diagnostics;
using Core.Disposable;
using Ui.Core.Text;
using UnityEngine;
using UnityEngine.Events;

namespace Ui.Core.Button
{
    [RequireComponent(typeof(UnityEngine.UI.Button))]
    [DisallowMultipleComponent]
    public class ButtonMono : MonoBehaviour
    {
        [SerializeField] private UnityEngine.UI.Button _button;
        [SerializeField] private TextMono _text;

        public string Text
        {
            get => _text.Text;
            set
            {
                if (!_text)
                    return;
                
                _text.Text = value;
            }
        }
        
        public IDisposable AddOnClick(UnityAction callback)
        {
            _button.onClick.AddListener(callback);
            return new DeferredAction().Add(() => _button.onClick.RemoveListener(callback));
        }

        [Conditional("UNITY_EDITOR")]
        private void OnValidate()
        {
            _button ??= GetComponent<UnityEngine.UI.Button>();
        }
    }
}