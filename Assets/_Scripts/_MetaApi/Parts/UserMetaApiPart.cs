using Cysharp.Threading.Tasks;
using Presentation.Data.User;
using Senders;

namespace _MetaApi.Parts
{
    public abstract class UserMetaApiPart
    {
        protected UserMetaApiPart()
        {
        }

        protected IHttpCommunicator HttpCommunicator { get; private set; } = null!;

        public virtual UniTask InitializeAsync(IHttpCommunicator communicator)
        {
            HttpCommunicator = communicator;
            return UniTask.CompletedTask;
        }

        public virtual UniTask DeInitialize()
        {
            return UniTask.CompletedTask;
        }

        public virtual UniTask OnChangedAsync(UserDto userDto)
        {
            return UniTask.CompletedTask;
        }
    }
}