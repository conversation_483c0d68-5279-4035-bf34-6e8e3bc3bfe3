using Core.Rx;
using Cysharp.Threading.Tasks;
using Presentation.Data.User;
using UnityEngine;

namespace _MetaApi.Parts.Progress
{
    public class UserProgressMetaApiPart : UserMetaApiPart, IUserProgressMetaApiPart
    {
        private readonly Rx<int> _money = new Rx<int>();

        public IRx<int> Money => _money;
        
        private readonly Rx<int> _gems = new Rx<int>();

        public IRx<int> Gems => _gems;
        
        public override UniTask OnChangedAsync(UserDto userDto)
        {
            _money.Value = userDto.Progress.Money;
            _gems.Value = userDto.Progress.Gems;
            
            return base.OnChangedAsync(userDto);
        }
    }
}