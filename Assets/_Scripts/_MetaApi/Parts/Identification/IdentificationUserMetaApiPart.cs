using Cysharp.Threading.Tasks;
using Presentation.Data.User;
using UnityEngine;

namespace _MetaApi.Parts.Identification
{
    public class IdentificationUserMetaApiPart : UserMetaApiPart, IIdentificationUserMetaApiPart
    {
        public int Id { get; private set; }
        public string Nickname { get; private set; }
        
        public override UniTask OnChangedAsync(UserDto userDto)
        {
            Id = userDto.Id;
            Nickname = userDto.Identification.Name;
            return UniTask.CompletedTask;
        }
    }
}