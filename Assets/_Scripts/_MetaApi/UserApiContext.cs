using _MetaApi.Parts;
using _MetaApi.Parts.Identification;
using _MetaApi.Parts.Progress;
using Cysharp.Threading.Tasks;
using Presentation.Data.User;
using RequestsResponses.Responses.User;
using Senders;
using UnityEngine;

namespace _MetaApi
{
    public class UserApiContext : IUserApiContext
    {
        private const string SectionUrl = "users";
        private const string UserId = "1";
        
        private readonly IHttpCommunicator _communicator;

        public UserApiContext(IHttpCommunicator communicator)
        {
            _communicator = communicator;
        }

        public IIdentificationUserMetaApiPart Identification { get; private set; } = null!;
        public IUserProgressMetaApiPart Progress { get; private set; } = null!;
        
        public async UniTask InitAsync()
        {
            var user = await _communicator.SendAsync<GetUserResponse>(
                HttpRequestParameters
                    .New(SectionUrl, HttpMethod.Get)
                    .SetRoute(UserId)
                );

            var userDto = user.User;
            
            Identification = await InitPartAsync<IIdentificationUserMetaApiPart, IdentificationUserMetaApiPart>(userDto);
            Progress = await InitPartAsync<IUserProgressMetaApiPart, UserProgressMetaApiPart>(userDto);
        }

        private async UniTask<TInterface> InitPartAsync<TInterface, TPart>(UserDto userDto)
            where TPart : UserMetaApiPart, TInterface, new()
        {
            var part = new TPart();
            await part.InitializeAsync(_communicator);
            await part.OnChangedAsync(userDto);
            return part;
        }
    }
}