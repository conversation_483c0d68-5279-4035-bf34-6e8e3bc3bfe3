using Cysharp.Threading.Tasks;

namespace Senders
{
    public interface IHttpCommunicator
    {
        UniTask<TResponse> SendAsync<TResponse>(HttpRequestParameters parameters)
            where TResponse : class;
        
        UniTask<TResponse> SendAsync<TRequest, TResponse>(HttpRequestParameters parameters, TRequest body)
            where TResponse : class
            where TRequest : class;
    }
}