using System;
using Core.Logger;
using Cysharp.Threading.Tasks;
using MessagePack;
using UnityEngine;
using UnityEngine.Networking;
using UnityEngine.Scripting;

namespace Senders
{
    [Preserve]
    public class HttpCommunicator : IHttpCommunicator
    {
        private static readonly Log _logger = new (nameof(HttpCommunicator));
        
        private const string ApiVersion = "v1";
        private const string EndPoint = "localhost:5292/api";

        [Preserve]
        public HttpCommunicator()
        {
        }

        public UniTask<TResponse> SendAsync<TResponse>(HttpRequestParameters parameters) 
            where TResponse : class
            => SendInternalAsync<TResponse>(parameters);
        
        public async UniTask<TResponse> SendAsync<TRequest, TResponse>(HttpRequestParameters parameters, TRequest body)
            where TResponse : class
            where TRequest : class
        {
            if (!parameters.IsValid())
                throw new Exception("Not valid request");

            try
            {
                var bytes = MessagePackSerializer.Serialize(body, options: MessagePackSerializerOptions.Standard);
                await SendInternalAsync<TResponse>(parameters, bytes);
            }
            catch (Exception exception)
            {
                _logger.Exception(exception);
                throw;
            }

            return null;
        }

        private static async UniTask<TResponse> SendInternalAsync<TResponse>(HttpRequestParameters parameters, byte[] bytes = null)
            where TResponse : class
        {
            try
            {
                UploadHandlerRaw uploadHandler = null;
                if (bytes is { Length: > 0 })
                    uploadHandler = new UploadHandlerRaw(bytes);

                var url = await parameters.GenerateUrlAsync(ApiVersion, EndPoint);
                _logger.Info($"Api send: {url}");
                using var webRequest = new UnityWebRequest(url, parameters.HttpMethod);
                webRequest.uploadHandler = uploadHandler;
                webRequest.downloadHandler = new DownloadHandlerBuffer();
                webRequest.SetRequestHeader("accept", "application/x-msgpack");
                webRequest.SetRequestHeader("Content-Type", "application/x-msgpack");
                

                var result = await webRequest.SendWebRequest();
                _logger.Info($"Api response get: {url} {result.error}");
                return MessagePackSerializer.Deserialize<TResponse>(result.downloadHandler.data, options: MessagePackSerializerOptions.Standard);
            }
            catch (Exception exception)
            {
                _logger.Exception(exception);
                throw;
            }
        }
    }
}