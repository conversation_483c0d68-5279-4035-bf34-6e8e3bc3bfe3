using System.Collections.Generic;
using System.Text;
using Cysharp.Threading.Tasks;

namespace Senders
{
    public class HttpRequestParameters
    {
        public string Section { get; }
        public string HttpMethod { get; }

        public string ServerMethodSignature { get; private set; } = null;
        public string Route { get; private set; } = null;
        public IReadOnlyList<KeyValuePair<string, string>> QueryParameters { get; private set; } = null;
        
        private HttpRequestParameters(string section, string httpMethod)
        {
            Section = section;
            HttpMethod = httpMethod;
        }

        public static HttpRequestParameters New(string section, string httpMethod)
        {
            return new(section, httpMethod);
        }
        
        public HttpRequestParameters SetServerMethod(string serverMethod)
        {
            ServerMethodSignature = serverMethod;
            return this;
        }

        public HttpRequestParameters SetRoute(string route)
        {
            Route = route;
            return this;
        }

        public HttpRequestParameters SetQueryParameters(IReadOnlyList<KeyValuePair<string, string>> queryParameters)
        {
            QueryParameters = queryParameters;
            return this;
        }

        public bool IsValid()
        {
            return !string.IsNullOrEmpty(Section) &&
                   !string.IsNullOrEmpty(ServerMethodSignature) &&
                   !string.IsNullOrEmpty(HttpMethod);
        }

        public UniTask<string> GenerateUrlAsync(string apiVersion, string endPoint)
        {
            return UniTask.RunOnThreadPool(() =>
                {
                    var sb = new StringBuilder();
                    sb.Append(endPoint);
                    sb.Append("/");
                    sb.Append(apiVersion);
                    sb.Append("/");
                    sb.Append(Section);
                    sb.Append("/");
                    if (!string.IsNullOrEmpty(ServerMethodSignature))
                    {
                        sb.Append(ServerMethodSignature);
                        sb.Append("/");
                    }
                    if (!string.IsNullOrEmpty(Route))
                    {
                        sb.Append(Route);
                        sb.Append("/");
                    }

                    if (QueryParameters is { Count: > 0 })
                    {
                        sb.Append("?");
                        for (var i = 0; i < QueryParameters.Count; ++i)
                        {
                            var kvp = QueryParameters[i];
                            sb.Append(kvp.Key);
                            sb.Append("=");
                            sb.Append(kvp.Value);

                            if (i < QueryParameters.Count - 1)
                                sb.Append("&");
                        }
                    }

                    return sb.ToString();
                }
            );
        }
    }
}