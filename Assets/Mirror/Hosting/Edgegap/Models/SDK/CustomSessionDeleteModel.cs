using System;
using System.Text;
using System.Collections;
using System.Collections.Generic;
using System.Runtime.Serialization;
using Newtonsoft.Json;

namespace IO.Swagger.Model {

  /// <summary>
  /// 
  /// </summary>
  [DataContract]
  public class CustomSessionDeleteModel {
    /// <summary>
    /// List of Custom IDs to Delete
    /// </summary>
    /// <value>List of Custom IDs to Delete</value>
    [DataMember(Name="sessions", EmitDefaultValue=false)]
    [JsonProperty(PropertyName = "sessions")]
    public List<string> Sessions { get; set; }


    /// <summary>
    /// Get the string presentation of the object
    /// </summary>
    /// <returns>String presentation of the object</returns>
    public override string ToString()  {
      StringBuilder sb = new StringBuilder();
      sb.Append("class CustomSessionDeleteModel {\n");
      sb.Append("  Sessions: ").Append(Sessions).Append("\n");
      sb.Append("}\n");
      return sb.ToString();
    }

    /// <summary>
    /// Get the JSON string presentation of the object
    /// </summary>
    /// <returns>JSON string presentation of the object</returns>
    public string ToJson() {
      return JsonConvert.SerializeObject(this, Formatting.Indented);
    }

}
}
