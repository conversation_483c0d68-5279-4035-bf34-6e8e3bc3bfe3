.row__port-table {
    padding: 2px 4px;
    display: flex;
    flex-direction: row;
    align-items: center;
    width: auto;
    justify-content: space-around;
    -unity-text-align: middle-left;
}

    .row__port-table > * {
        width: 0px;
        flex-grow: 1;
        align-self: center;
        margin: 0px;
        padding: 0px;
    }

.focusable:hover {
    background-color: rgba(0,0,0,0.2);
    border-radius: 3px;
}

.row__dns {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: auto;
    justify-content: space-between;
    -unity-text-align: middle-left;
}

.row__status {
    display: flex;
    flex-direction: row;
    align-items: center;
    width: auto;
    justify-content: space-between;
    -unity-text-align: middle-left;
}

.label__header {
    -unity-font-style: bold
}

.label__status {
    -unity-font-style: bold;
    border-radius: 2px;
    width: 100px;
    color: #fff;
    -unity-text-align: middle-center;
}

.label__info-text {
    padding: 8px;
    margin: 4px;
    border-radius: 3px;
    -unity-text-align: middle-center;
    white-space: normal;
    background-color: rgba(42, 42, 42, 0.5);
}

.container {
    margin: 8px 0px;
}

.bg--secondary {
    background-color: #8a8a8a;
}

.bg--success {
    background-color: #90be6d;
}

.bg--danger {
    background-color: #f94144;
}

.bg--warning {
    background-color: #f9c74f;
}
