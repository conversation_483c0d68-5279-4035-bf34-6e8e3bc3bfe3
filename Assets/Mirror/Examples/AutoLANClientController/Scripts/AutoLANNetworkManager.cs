using System;
using UnityEngine;
using UnityEngine.SceneManagement;
using Mirror;
using System.Linq;

/*
	Documentation: https://mirror-networking.gitbook.io/docs/components/network-manager
	API Reference: https://mirror-networking.com/docs/api/Mirror.NetworkManager.html
*/
namespace Mirror.Examples.AutoLANClientController
{
    public class AutoLANNetworkManager : NetworkManager
    {
        // Overrides the base singleton so we don't
        // have to cast to this type everywhere.
        public static new AutoLANNetworkManager singleton { get; private set; }

        public CanvasHUD canvasHUD;
        private NetworkIdentity[] copyOfOwnedObjects;

        /// <summary>
        /// Runs on both Server and Client
        /// Networking is NOT initialized when this fires
        /// </summary>
        public override void Awake()
        {
            base.Awake();
            singleton = this;
        }

        #region Unity Callbacks

        public override void OnValidate()
        {
            base.OnValidate();
        }

        /// <summary>
        /// Runs on both Server and Client
        /// Networking is NOT initialized when this fires
        /// </summary>
        public override void Start()
        {
            base.Start();
            if (canvasHUD == null)
            {
#if UNITY_2022_2_OR_NEWER
                canvasHUD = GameObject.FindAnyObjectByType<CanvasHUD>();
#else
                // Deprecated in Unity 2023.1
                canvasHUD = GameObject.FindObjectOfType<CanvasHUD>();
#endif
            }
        }

        /// <summary>
        /// Runs on both Server and Client
        /// </summary>
        public override void LateUpdate()
        {
            base.LateUpdate();
        }

        /// <summary>
        /// Runs on both Server and Client
        /// </summary>
        public override void OnDestroy()
        {
            base.OnDestroy();
            //UnityEngine.Debug.Log("OnDestroy");
        }

#endregion

        #region Start & Stop

        /// <summary>
        /// Set the frame rate for a headless server.
        /// <para>Override if you wish to disable the behavior or set your own tick rate.</para>
        /// </summary>
        public override void ConfigureHeadlessFrameRate()
        {
            base.ConfigureHeadlessFrameRate();
        }

        /// <summary>
        /// called when quitting the application by closing the window / pressing stop in the editor
        /// </summary>
        public override void OnApplicationQuit()
        {
            base.OnApplicationQuit();
            //UnityEngine.Debug.Log("OnApplicationQuit");
        }

        #endregion

        #region Scene Management

        /// <summary>
        /// This causes the server to switch scenes and sets the networkSceneName.
        /// <para>Clients that connect to this server will automatically switch to this scene. This is called automatically if onlineScene or offlineScene are set, but it can be called from user code to switch scenes again while the game is in progress. This automatically sets clients to be not-ready. The clients must call NetworkClient.Ready() again to participate in the new scene.</para>
        /// </summary>
        /// <param name="newSceneName"></param>
        public override void ServerChangeScene(string newSceneName)
        {
            base.ServerChangeScene(newSceneName);
            //UnityEngine.Debug.Log("ServerChangeScene");
        }

        /// <summary>
        /// Called from ServerChangeScene immediately before SceneManager.LoadSceneAsync is executed
        /// <para>This allows server to do work / cleanup / prep before the scene changes.</para>
        /// </summary>
        /// <param name="newSceneName">Name of the scene that's about to be loaded</param>
        public override void OnServerChangeScene(string newSceneName)
        {
            //UnityEngine.Debug.Log("OnServerChangeScene");
        }

        /// <summary>
        /// Called on the server when a scene is completed loaded, when the scene load was initiated by the server with ServerChangeScene().
        /// </summary>
        /// <param name="sceneName">The name of the new scene.</param>
        public override void OnServerSceneChanged(string sceneName)
        {
            //UnityEngine.Debug.Log("OnServerSceneChanged");
        }

        /// <summary>
        /// Called from ClientChangeScene immediately before SceneManager.LoadSceneAsync is executed
        /// <para>This allows client to do work / cleanup / prep before the scene changes.</para>
        /// </summary>
        /// <param name="newSceneName">Name of the scene that's about to be loaded</param>
        /// <param name="sceneOperation">Scene operation that's about to happen</param>
        /// <param name="customHandling">true to indicate that scene loading will be handled through overrides</param>
        public override void OnClientChangeScene(string newSceneName, SceneOperation sceneOperation, bool customHandling)
        {
            //UnityEngine.Debug.Log("OnClientChangeScene");
        }

        /// <summary>
        /// Called on clients when a scene has completed loaded, when the scene load was initiated by the server.
        /// <para>Scene changes can cause player objects to be destroyed. The default implementation of OnClientSceneChanged in the NetworkManager is to add a player object for the connection if no player object exists.</para>
        /// </summary>
        public override void OnClientSceneChanged()
        {
            base.OnClientSceneChanged();
            // UnityEngine.Debug.Log("OnClientSceneChanged");
        }

        #endregion

        #region Server System Callbacks

        /// <summary>
        /// Called on the server when a new client connects.
        /// <para>Unity calls this on the Server when a Client connects to the Server. Use an override to tell the NetworkManager what to do when a client connects to the server.</para>
        /// </summary>
        /// <param name="conn">Connection from client.</param>
        public override void OnServerConnect(NetworkConnectionToClient conn)
        {
            //UnityEngine.Debug.Log("OnServerConnect");
            canvasHUD.SetupInfoText("A client connected.");
        }

        /// <summary>
        /// Called on the server when a client is ready.
        /// <para>The default implementation of this function calls NetworkServer.SetClientReady() to continue the network setup process.</para>
        /// </summary>
        /// <param name="conn">Connection from client.</param>
        public override void OnServerReady(NetworkConnectionToClient conn)
        {
            base.OnServerReady(conn);
            //UnityEngine.Debug.Log("OnServerReady");
        }

        /// <summary>
        /// Called on the server when a client adds a new player with ClientScene.AddPlayer.
        /// <para>The default implementation for this function creates a new player object from the playerPrefab.</para>
        /// </summary>
        /// <param name="conn">Connection from client.</param>
        public override void OnServerAddPlayer(NetworkConnectionToClient conn)
        {
            base.OnServerAddPlayer(conn);
            //UnityEngine.Debug.Log("OnServerAddPlayer");
        }

        /// <summary>
        /// Called on the server when a client disconnects.
        /// <para>This is called on the Server when a Client disconnects from the Server. Use an override to decide what should happen when a disconnection is detected.</para>
        /// </summary>
        /// <param name="conn">Connection from client.</param>
        public override void OnServerDisconnect(NetworkConnectionToClient conn)
        {
            // this code is to reset any objects belonging to disconnected clients
            // make a copy because the original collection will change in the loop
            copyOfOwnedObjects = conn.owned.ToArray();
            // Loop the copy, skipping the player object.
            // RemoveClientAuthority on everything else
            foreach (NetworkIdentity identity in copyOfOwnedObjects)
            {
                if (identity != conn.identity)
                    identity.RemoveClientAuthority();
            }

            base.OnServerDisconnect(conn);
            //UnityEngine.Debug.Log("OnServerDisconnect");
            canvasHUD.SetupInfoText("A client disconnected.");
        }

        /// <summary>
        /// Called on server when transport raises an error.
        /// <para>NetworkConnection may be null.</para>
        /// </summary>
        /// <param name="conn">Connection of the client...may be null</param>
        /// <param name="transportError">TransportError enum</param>
        /// <param name="message">String message of the error.</param>
        public override void OnServerError(NetworkConnectionToClient conn, TransportError transportError, string message)
        {
            UnityEngine.Debug.Log("OnServerError");
            canvasHUD.SetupInfoText("OnServerError: " + message);
        }

        #endregion

        #region Client System Callbacks

        /// <summary>
        /// Called on the client when connected to a server.
        /// <para>The default implementation of this function sets the client as ready and adds a player. Override the function to dictate what happens when the client connects.</para>
        /// </summary>
        public override void OnClientConnect()
        {
            base.OnClientConnect();
            //UnityEngine.Debug.Log("OnClientConnect");
            canvasHUD.SetupInfoText("Connected to server.");
        }

        /// <summary>
        /// Called on clients when disconnected from a server.
        /// <para>This is called on the client when it disconnects from the server. Override this function to decide what happens when the client disconnects.</para>
        /// </summary>
        public override void OnClientDisconnect()
        {
            //UnityEngine.Debug.Log("OnClientDisconnect");
            canvasHUD.SetupInfoText("Disconnected from server.");
        }

        /// <summary>
        /// Called on clients when a servers tells the client it is no longer ready.
        /// <para>This is commonly used when switching scenes.</para>
        /// </summary>
        public override void OnClientNotReady()
        {
            //UnityEngine.Debug.Log("OnClientNotReady");
        }

        /// <summary>
        /// Called on client when transport raises an error.</summary>
        /// </summary>
        /// <param name="transportError">TransportError enum.</param>
        /// <param name="message">String message of the error.</param>
        public override void OnClientError(TransportError transportError, string message)
        {
            UnityEngine.Debug.Log("OnClientError");
            canvasHUD.SetupInfoText("OnClientError: " + message);
        }

        #endregion

        #region Start & Stop Callbacks

        // Since there are multiple versions of StartServer, StartClient and StartHost, to reliably customize
        // their functionality, users would need override all the versions. Instead these callbacks are invoked
        // from all versions, so users only need to implement this one case.

        /// <summary>
        /// This is invoked when a host is started.
        /// <para>StartHost has multiple signatures, but they all cause this hook to be called.</para>
        /// </summary>
        public override void OnStartHost()
        {
            //UnityEngine.Debug.Log("OnStartHost");
            canvasHUD.SetupInfoText("Started Hosting.");
        }

        /// <summary>
        /// This is invoked when a server is started - including when a host is started.
        /// <para>StartServer has multiple signatures, but they all cause this hook to be called.</para>
        /// </summary>
        public override void OnStartServer()
        {
            //UnityEngine.Debug.Log("OnStartServer");
            canvasHUD.SetupInfoText("Started server.");
        }

        /// <summary>
        /// This is invoked when the client is started.
        /// </summary>
        public override void OnStartClient()
        {
            //UnityEngine.Debug.Log("OnStartClient");
            canvasHUD.SetupInfoText("Client started.");
        }

        /// <summary>
        /// This is called when a host is stopped.
        /// </summary>
        public override void OnStopHost()
        {
            //UnityEngine.Debug.Log("OnStopHost");
            canvasHUD.SetupInfoText("Hosting stopped.");
        }

        /// <summary>
        /// This is called when a server is stopped - including when a host is stopped.
        /// </summary>
        public override void OnStopServer()
        {
            //UnityEngine.Debug.Log("OnStopServer");
            canvasHUD.SetupInfoText("Server stopped.");
        }

        /// <summary>
        /// This is called when a client is stopped.
        /// </summary>
        public override void OnStopClient()
        {
            //UnityEngine.Debug.Log("OnStopClient");
            canvasHUD.SetupInfoText("Client stopped.");
        }

        #endregion
    }
}
