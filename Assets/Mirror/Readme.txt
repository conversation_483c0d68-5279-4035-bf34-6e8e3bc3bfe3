Mirror is a MMO Scale Networking library for Unity, used in uMMORPG, uSurvival and several MMO projects in development.

*** IMPORTANT ***
You must restart Unity after importing Mirror for the Components Menu to update!

Requirements:
    Unity 2019 / 2020 LTS
    Runtime .Net 4.x (Project Settings > Player > Other Settings)

Documentation:
    https://mirror-networking.gitbook.io/docs/

Support:
    Discord: https://discordapp.com/invite/N9QVxbM
    Bug Reports: https://github.com/vis2k/Mirror/issues
