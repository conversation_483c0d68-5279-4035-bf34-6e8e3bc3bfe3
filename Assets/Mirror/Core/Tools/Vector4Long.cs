#pragma warning disable CS0659 // 'Vector4Long' overrides Object.Equals(object o) but does not override Object.GetHashCode()
#pragma warning disable CS0661 // 'Vector4Long' defines operator == or operator != but does not override Object.GetHashCode()

// Vector4Long by mischa (based on game engine project)
using System;
using System.Runtime.CompilerServices;

namespace Mirror
{
    public struct Vector4Long
    {
        public long x;
        public long y;
        public long z;
        public long w;

        public static readonly Vector4Long zero = new Vector4Long(0, 0, 0, 0);
        public static readonly Vector4Long one = new Vector4Long(1, 1, 1, 1);

        // constructor /////////////////////////////////////////////////////////
        public Vector4Long(long x, long y, long z, long w)
        {
            this.x = x;
            this.y = y;
            this.z = z;
            this.w = w;
        }

        // operators ///////////////////////////////////////////////////////////
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector4Long operator +(Vector4Long a, Vector4Long b) =>
            new Vector4Long(a.x + b.x, a.y + b.y, a.z + b.z, a.w + b.w);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector4Long operator -(Vector4Long a, Vector4Long b) =>
            new Vector4Long(a.x - b.x, a.y - b.y, a.z - b.z, a.w - b.w);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector4Long operator -(Vector4Long v) =>
            new Vector4Long(-v.x, -v.y, -v.z, -v.w);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector4Long operator *(Vector4Long a, long n) =>
            new Vector4Long(a.x * n, a.y * n, a.z * n, a.w * n);

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static Vector4Long operator *(long n, Vector4Long a) =>
            new Vector4Long(a.x * n, a.y * n, a.z * n, a.w * n);

        // == returns true if approximately equal (with epsilon).
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool operator ==(Vector4Long a, Vector4Long b) =>
            a.x == b.x &&
            a.y == b.y &&
            a.z == b.z &&
            a.w == b.w;

        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public static bool operator !=(Vector4Long a, Vector4Long b) => !(a == b);

        // NO IMPLICIT System.Numerics.Vector4Long conversion because double<->float
        // would silently lose precision in large worlds.

        // [i] component index. useful for iterating all components etc.
        public long this[int index]
        {
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            get
            {
                switch (index)
                {
                    case 0: return x;
                    case 1: return y;
                    case 2: return z;
                    case 3: return w;
                    default: throw new IndexOutOfRangeException($"Vector4Long[{index}] out of range.");
                }
            }
            [MethodImpl(MethodImplOptions.AggressiveInlining)]
            set
            {
                switch (index)
                {
                    case 0:
                        x = value;
                        break;
                    case 1:
                        y = value;
                        break;
                    case 2:
                        z = value;
                        break;
                    case 3:
                        w = value;
                        break;
                    default: throw new IndexOutOfRangeException($"Vector4Long[{index}] out of range.");
                }
            }
        }

        // instance functions //////////////////////////////////////////////////
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override string ToString() => $"({x} {y} {z} {w})";

        // equality ////////////////////////////////////////////////////////////
        // implement Equals & HashCode explicitly for performance.
        // calling .Equals (instead of "==") checks for exact equality.
        // (API compatibility)
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public bool Equals(Vector4Long other) =>
            x == other.x && y == other.y && z == other.z && w == other.w;

        // Equals(object) can reuse Equals(Vector4)
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override bool Equals(object other) =>
            other is Vector4Long vector4 && Equals(vector4);

#if UNITY_2021_3_OR_NEWER
        // Unity 2019/2020 don't have HashCode.Combine yet.
        // this is only to avoid reflection. without defining, it works too.
        // default generated by rider
        [MethodImpl(MethodImplOptions.AggressiveInlining)]
        public override int GetHashCode() => HashCode.Combine(x, y, z, w);
#endif
    }
}
