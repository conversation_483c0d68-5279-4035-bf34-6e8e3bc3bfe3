{"name": "Mirror.Transports", "rootNamespace": "", "references": ["GUID:30817c1a0e6d646d99c048fc403f5979", "GUID:6806a62c384838046a3c66c44f06d75f", "GUID:725ee7191c021de4dbf9269590ded755", "GUID:3b5390adca4e2bb4791cb930316d6f3e"], "includePlatforms": [], "excludePlatforms": [], "allowUnsafeCode": true, "overrideReferences": false, "precompiledReferences": [], "autoReferenced": true, "defineConstraints": [], "versionDefines": [], "noEngineReferences": false}